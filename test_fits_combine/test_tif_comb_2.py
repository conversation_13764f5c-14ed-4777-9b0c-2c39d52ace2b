import tifffile
import numpy as np


def combine_fits_files(file1, file2, output_file):
    # 读取TIFF文件
    data1 = tifffile.imread(file1)  # 替换fits.open
    data2 = tifffile.imread(file2)  # 替换fits.open

    # 在 y 方向上拼接数据
    combined_data = np.concatenate((data1, data2), axis=1)

    # 保存为TIFF文件
    tifffile.imwrite(output_file, combined_data)  # 替换writeto


def extract_and_save_region(file1, file2, output_file, x_start, y_start, width, height):
    # 读取TIFF文件
    data1 = tifffile.imread(file1)  # 替换fits.open
    data2 = tifffile.imread(file2)  # 替换fits.open

    # 提取指定区域
    region1 = data1[y_start:y_start + height, x_start:x_start + width]
    region2 = data2[y_start:y_start + height, x_start:x_start + width]

    # y方向翻转 region2
    region2 = np.flipud(region2)

    # 在 y 方向上拼接数据
    combined_region = np.concatenate((region1, region2), axis=1)

    # 保存为TIFF文件
    tifffile.imwrite(output_file, combined_region)


if __name__ == "__main__":
    combine_fits_files('1.tif', '2.tif', '3.tif')  # 注意文件扩展名改为.tiff
    extract_and_save_region('1.tif', '2.tif', '4.tif', 48, 92, 14304-48-48, 10748-92)
