import astropy.io.fits as fits
import numpy as np

def combine_fits_files(file1, file2, output_file):
    # 加载 FITS 文件
    hdu1 = fits.open(file1)[0]
    hdu2 = fits.open(file2)[0]
    
    # 获取数据
    data1 = hdu1.data
    data2 = hdu2.data
    
    # 在 y 方向上拼接数据
    combined_data = np.concatenate((data1, data2), axis=0)
    
    # 创建新的 HDU
    hdu_combined = fits.PrimaryHDU(combined_data)
    
    # 保存为新的 FITS 文件
    hdu_combined.writeto(output_file, overwrite=True)

def extract_and_save_region(file1, file2, output_file, x_start, y_start, width, height):
    # 加载 FITS 文件
    hdu1 = fits.open(file1)[0]
    hdu2 = fits.open(file2)[0]
    
    # 获取数据
    data1 = hdu1.data
    data2 = hdu2.data
    
    # 提取指定区域
    region1 = data1[y_start:y_start+height, x_start:x_start+width]
    region2 = data2[y_start:y_start+height, x_start:x_start+width]

    # y方向翻转 region2
    region2 = np.flipud(region2)

    # 在 y 方向上拼接数据
    combined_region = np.concatenate((region1, region2), axis=0)
    
    # 创建新的 HDU
    hdu_combined = fits.PrimaryHDU(combined_region)
    
    # 保存为新的 FITS 文件
    hdu_combined.writeto(output_file, overwrite=True)


if __name__ == "__main__":
    combine_fits_files('1.fits', '2.fits', '3.fits')
    extract_and_save_region('1.fits', '2.fits', '4.fits', 0, 0, 4000, 800)
