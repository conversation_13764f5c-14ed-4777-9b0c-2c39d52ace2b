import re

kzone_to_ra_dec_data = [
    ("K001", 1.50000, +75.0),
    ("K002", 6.00000, +75.0),
    ("K003", 9.00000, +75.0),
    ("K004", 12.00000, +75.0),
    ("K005", 15.00000, +75.0),
    ("K006", 18.00000, +75.0),
    ("K007", 22.50000, +75.0),
    ("K008", 1.45455, +57.0),
    ("K009", 2.90909, +57.0),
    ("K010", 5.09091, +57.0),
    ("K011", 7.27273, +57.0),
    ("K012", 8.72727, +57.0),
    ("K013", 10.90909, +57.0),
    ("K014", 12.36364, +57.0),
    ("K015", 14.54545, +57.0),
    ("K016", 16.72727, +57.0),
    ("K017", 18.18182, +57.0),
    ("K018", 20.36364, +57.0),
    ("K019", 21.81818, +57.0),
    ("K020", 23.27273, +57.0),
    ("K021", 1.02128, +39.0),
    ("K022", 2.55319, +39.0),
    ("K023", 3.57447, +39.0),
    ("K024", 5.10638, +39.0),
    ("K025", 6.63830, +39.0),
    ("K026", 8.17021, +39.0),
    ("K027", 9.19149, +39.0),
    ("K028", 10.72340, +39.0),
    ("K029", 12.25532, +39.0),
    ("K030", 13.78723, +39.0),
    ("K031", 14.80851, +39.0),
    ("K032", 16.34043, +39.0),
    ("K033", 17.87234, +39.0),
    ("K034", 19.40426, +39.0),
    ("K035", 20.93617, +39.0),
    ("K036", 21.95745, +39.0),
    ("K037", 23.48936, +39.0),
    ("K038", 0.84211, +21.0),
    ("K039", 2.10526, +21.0),
    ("K040", 3.36842, +21.0),
    ("K041", 4.63158, +21.0),
    ("K042", 5.89474, +21.0),
    ("K043", 7.15789, +21.0),
    ("K044", 8.42105, +21.0),
    ("K045", 9.68421, +21.0),
    ("K046", 10.94737, +21.0),
    ("K047", 11.78947, +21.0),
    ("K048", 13.05263, +21.0),
    ("K049", 14.31579, +21.0),
    ("K050", 15.57895, +21.0),
    ("K051", 16.84211, +21.0),
    ("K052", 18.10526, +21.0),
    ("K053", 19.36842, +21.0),
    ("K054", 20.63158, +21.0),
    ("K055", 21.89474, +21.0),
    ("K056", 23.15789, +21.0),
    ("K057", 0.42105, +21.0),
    ("K058", 0.80000, +3.0),
    ("K059", 2.00000, +3.0),
    ("K060", 3.20000, +3.0),
    ("K061", 4.40000, +3.0),
    ("K062", 5.60000, +3.0),
    ("K063", 6.80000, +3.0),
    ("K064", 8.00000, +3.0),
    ("K065", 9.20000, +3.0),
    ("K066", 10.40000, +3.0),
    ("K067", 11.60000, +3.0),
    ("K068", 12.80000, +3.0),
    ("K069", 14.00000, +3.0),
    ("K070", 15.20000, +3.0),
    ("K071", 16.40000, +3.0),
    ("K072", 17.60000, +3.0),
    ("K073", 18.80000, +3.0),
    ("K074", 20.00000, +3.0),
    ("K075", 21.20000, +3.0),
    ("K076", 22.40000, +3.0),
    ("K077", 23.60000, +3.0),
    ("K078", 0.82759, -15.0),
    ("K079", 2.06897, -15.0),
    ("K080", 3.31034, -15.0),
    ("K081", 4.55172, -15.0),
    ("K082", 5.79310, -15.0),
    ("K083", 6.62069, -15.0),
    ("K084", 7.86207, -15.0),
    ("K085", 9.10345, -15.0),
    ("K086", 10.34483, -15.0),
    ("K087", 11.58621, -15.0),
    ("K088", 12.82759, -15.0),
    ("K089", 14.06897, -15.0),
    ("K090", 15.31034, -15.0),
    ("K091", 16.55172, -15.0),
    ("K092", 17.79310, -15.0),
    ("K093", 18.62069, -15.0),
    ("K094", 19.86207, -15.0),
    ("K095", 21.10345, -15.0),
    ("K096", 22.34483, -15.0),
    ("K097", 23.58621, -15.0),
]

k_data_dict = {}

# 遍历数据列表，将每行转换为字典，并添加到 data_dict 中
for row in kzone_to_ra_dec_data:
    key = row[0]  # 第一列数据作为键
    value = row[1:]  # 剩余列数据作为值
    k_data_dict[key] = value


# 输出结果
# print(data_dict)
# print(f'{data_dict["K094"][0]}')
# print(f'{data_dict["K094"][1]}')


def get_ra_dec_from_path(url_path):
    # 定义正则表达式模式来匹配 K028 K027 等字段
    pattern = r'K\d{3}'

    # 使用 re.findall 查找所有匹配的字段
    matches = re.findall(pattern, url_path)
    k_string = None
    if len(matches) > 1:
        k_string = matches[0]
        # print(f'++K__ ra dec   {matches[0]}  {k_data_dict[k_string][0]}   {k_data_dict[k_string][1]}')
    else:
        print(f'--K__')
        if '_KK' in url_path:
            k_string = 'K021'
        else:
            if '_ST_' in url_path:
                k_string = 'K021'
            else:
                # GY3_38-1
                k_string = 'K038'

        print(f'--{k_string}')

    return k_data_dict[k_string]


# url_str = r'https://download.china-vo.org/psp/KATS/GY6-DATA/2024/20240118/K085/GY6_K085-9_No%20Filter_60S_Bin2_UTC20240118_191918_-25C_.fit'
# get_ra_dec_from_path(url_str)
