# /*
# * 蔡勒公式
# * 1582.10.4之后  w = (y + y/4 + c/4 - 2*c + (26*(m+1))/10 + d - 1) % 7
# * 1582.10.4及之前 w = (y + y/4 + c/4 -2*c + (13*(m+1))/5 + d + 2) % 7;
# */
# //1582.10.4之后星期的计算
def week(y, m, d):
    if m < 3:
        m += 12
        y = y-1

    c = y / 100
    y = y - c*100
    w = (y + y/4 + c/4 - 2*c + (26*(m+1))/10 + d - 1) % 7
    w = (w + 7) % 7
    return w


print("%d" % week(2019, 9, 15))
print("%d" % week(2015, 4, 16))
print("%d" % week(1989, 2, 3))
print("%d" % week(2024, 3, 20))
print("%d" % week(2006, 4, 4))


# #include <stdio.h>
#
# /*
# * 蔡勒公式
# * 1582.10.4之后  w = (y + y/4 + c/4 - 2*c + (26*(m+1))/10 + d - 1) % 7
# * 1582.10.4及之前 w = (y + y/4 + c/4 -2*c + (13*(m+1))/5 + d + 2) % 7;
# */
# int week(int y, int m, int d)   //1582.10.4之后星期的计算
# {
#     if (m < 3) {
#         m += 12;
#         y--;
#     }
#     int c = y / 100;
#     y = y - c*100;
#     int w =  (y + y/4 + c/4 - 2*c + (26*(m+1))/10 + d - 1) % 7;
#     w = (w + 7) % 7;
#     return w;
# }
#
# int main()
# {
#     printf("%d\n", week(2019, 9, 15));  //=>0  星期天
#     printf("%d\n", week(2015, 4, 16));  // => 4 星期四
#     printf("%d\n", week(1989, 2, 3));   // => 5 星期五
#     printf("%d\n", week(2024, 3, 20));   // => 5 星期五
#     printf("%d\n", week(2006, 4, 4));   // => 5 星期五
#
#     return 0;
# }
