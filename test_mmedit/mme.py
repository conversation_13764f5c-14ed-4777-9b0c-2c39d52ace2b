import cv2
import numpy as np
from mmedit.apis import init_model, inference_model
from mmedit.utils import tensor2img

# 1. 定义模型配置文件和权重文件 (需要根据你选择的人脸交换模型修改)
config_file = 'configs/face_restoration/restoreformer/restoreformer_default.py'  # 这只是个示例，可能需要更换
checkpoint_file = 'checkpoints/RestoreFormer.pth'  # 这只是个示例，可能需要更换
# 再次强调，需要选择专门的人脸交换模型

# 2. 初始化模型
device = 'cuda:0' if cv2.cuda.getCudaEnabledDeviceCount() > 0 else 'cpu'  # 优先使用 CUDA
model = init_model(config_file, checkpoint_file, device=device)

# 3. 定义视频路径和源人脸图像路径
video_path = 'path/to/your/target_video.mp4'  # 目标视频 (要被替换人脸的视频)
source_image_path = 'path/to/your/source_face.jpg'  # 源人脸图像

# 4. 加载源人脸图像
source_img = cv2.imread(source_image_path)

# 5. 加载视频
video_capture = cv2.VideoCapture(video_path)
if not video_capture.isOpened():
    print("Error: Could not open video.")
    exit()

# 6. 获取视频的帧率和尺寸
fps = video_capture.get(cv2.CAP_PROP_FPS)
frame_width = int(video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
frame_height = int(video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))

# 7. 创建视频写入对象 (用于保存处理后的视频)
output_video_path = 'output_video.mp4'
fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 编码格式 (根据你的需求选择)
video_writer = cv2.VideoWriter(output_video_path, fourcc, fps, (frame_width, frame_height))

# 8. 人脸检测器 (使用 OpenCV 的 Haar Cascade)
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

# 9. 循环处理视频的每一帧
frame_count = 0
while True:
    ret, frame = video_capture.read()
    if not ret:
        break  # 视频结束

    frame_count += 1
    print(f"Processing frame: {frame_count}")

    # 10. 人脸检测
    gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray_frame, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))

    # 11. 遍历检测到的人脸
    for (x1, y1, w, h) in faces:
        x2, y2 = x1 + w, y1 + h

        # 12. 进行人脸交换 (和之前的图像示例类似，需要根据你选择的模型调整代码)
        try:
            # **重要:**  需要根据你的模型输入要求，调整 `frame` 的格式
            # 例如，有些模型需要 RGB 格式，有些需要缩放到特定大小
            # 以下只是示例，很可能需要修改
            results = inference_model(model, frame, face_crop=(x1, y1, x2, y2)) #传递包含人脸的图像
            output_face = tensor2img(results['restored_face'].cpu())  # 提取结果

            # 将交换后的人脸放回帧中 (简化处理，需要泊松融合)
            frame[y1:y2, x1:x2] = output_face
        except Exception as e:
            print(f"Error processing face: {e}")  # 打印错误信息，方便调试

    # 13. 将处理后的帧写入输出视频
    video_writer.write(frame)

    # 14. (可选) 显示处理后的帧 (用于调试)
    # cv2.imshow('Face Swapped Video', frame)
    # if cv2.waitKey(1) & 0xFF == ord('q'):  # 按 'q' 退出
    #     break

# 15. 释放资源
video_capture.release()
video_writer.release()
cv2.destroyAllWindows()

print(f"Video saved to: {output_video_path}")