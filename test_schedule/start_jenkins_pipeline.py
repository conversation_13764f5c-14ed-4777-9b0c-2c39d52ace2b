import requests
import time
from datetime import datetime, timedelta
import urllib3

# 禁用不安全请求警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Jenkins 配置
JENKINS_URL = "http://localhost:9090"
JENKINS_USER = ""
JENKINS_TOKEN = ""  # 在 Jenkins 用户配置中生成的 API token

def trigger_and_wait_pipeline(date_param):
    """
    通过 REST API 触发 Pipeline 并等待其完成
    """
    job_name = 'fits_pipeline_s2_s4'
    
    try:
        # 验证作业是否存在
        job_url = f"{JENKINS_URL}/job/{job_name}/api/json"
        response = requests.get(
            job_url,
            auth=(JENKINS_USER, JENKINS_TOKEN),
            verify=False
        )
        
        if response.status_code != 200:
            print(f"Error: Pipeline '{job_name}' does not exist!")
            return None

        # 触发构建
        build_url = f"{JENKINS_URL}/job/{job_name}/buildWithParameters"
        response = requests.post(
            build_url,
            params={'INPUT_DATE': date_param},
            auth=(JENKINS_USER, JENKINS_TOKEN),
            verify=False
        )

        if response.status_code != 201:
            print("Error: Failed to trigger build")
            return None

        # 从 Location header 获取队列项 ID
        queue_url = response.headers.get('Location')
        queue_id = queue_url.split('/')[-2]
        print(f"Build queued with queue item: {queue_id}")

        # 等待任务开始执行
        time.sleep(5)

        # 获取构建号
        build_number = None
        while build_number is None:
            queue_info_url = f"{JENKINS_URL}/queue/item/{queue_id}/api/json"
            response = requests.get(
                queue_info_url,
                auth=(JENKINS_USER, JENKINS_TOKEN),
                verify=False
            )
            
            if response.status_code == 200:
                queue_info = response.json()
                if 'executable' in queue_info:
                    build_number = queue_info['executable']['number']
                    break
            time.sleep(3)

        print(f"Started build #{build_number} for date {date_param}")

        # 等待构建完成
        while True:
            build_info_url = f"{JENKINS_URL}/job/{job_name}/{build_number}/api/json"
            response = requests.get(
                build_info_url,
                auth=(JENKINS_USER, JENKINS_TOKEN),
                verify=False
            )
            
            if response.status_code == 200:
                build_info = response.json()
                if not build_info['building']:
                    result = build_info['result']
                    print(f"Build #{build_number} completed with result: {result}")
                    return result
            time.sleep(10)

    except Exception as e:
        print(f"Error triggering pipeline: {str(e)}")
        return None

def main():
    # 生成要处理的日期列表
    start_date = datetime(2024, 11, 18)  # 开始日期
    end_date = datetime(2024, 11, 30)    # 结束日期
    
    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y%m%d')
        print(f"Processing date: {date_str}")
        
        # 触发 Pipeline 并等待完成
        result = trigger_and_wait_pipeline(date_str)
        
        if result != 'SUCCESS':
            print(f"Pipeline failed for date {date_str}")
            break
        
        # 移到下一天
        current_date += timedelta(days=1)

if __name__ == "__main__":
    main()
