# from astrometry_net_client import Client
#
# c = Client(api_key='piacanrvtuvjuifi')
#
# # WARNING: this can take a while, as it blocks until the file is solved.
# # wcs will be None if upload is not successful
# wcs = c.calibrate_file_wcs('E:/wsl/605320220625185638.fits')
#
# print(wcs)


from astrometry_net_client import Session
from astrometry_net_client import FileUpload

s = Session(api_key='piacanrvtuvjuifi')
# upl = FileUpload('E:/wsl/605320220625185638.fits', session=s) # 1.
upl = FileUpload('E:/wsl/605320220625185638.fits', session=s) # 1.
submission = upl.submit()                     # 2.
submission.until_done()                       # blocks until it is finished
job = submission.jobs[0]                      # 3.
job.until_done()                              # 4. (again blocks)
if job.success():
    wcs = job.wcs_file()                      # 5. (only if successful)
    print(wcs)
print(job.info())                             # works even though the job failed
