import requests


def call_kimi_api(api_url, headers=None, params=None, data=None):
    """
    调用Kimi API的函数

    :param api_url: API的URL
    :param headers: 请求头，如Authorization
    :param params: URL参数（GET请求）
    :param data: 请求体（POST请求）
    :return: API响应的JSON数据
    """
    try:
        if data:
            response = requests.post(api_url, headers=headers, json=data)
        else:
            response = requests.get(api_url, headers=headers, params=params)

        response.raise_for_status()  # 如果响应状态码不是200，抛出异常
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求出错: {e}")
        return None


# 示例调用
if __name__ == "__main__":
    # 替换为实际的API URL
    api_url = "https://api.example.com/kimi"

    # 如果API需要认证，设置请求头
    headers = {
        "Authorization": "sk-GZIcYiRLB4uf2s6UoNEdDKEJVd92z82oJjFKl8MlOwN4xxFV"
    }

    # 如果是GET请求，设置URL参数
    params = {
        "param1": "value1",
        "param2": "value2"
    }

    # 如果是POST请求，设置请求体
    data = {
        "key1": "value1",
        "key2": "value2"
    }

    # 调用API
    result = call_kimi_api(api_url, headers=headers, params=params, data=data)

    if result:
        print("API响应:", result)
