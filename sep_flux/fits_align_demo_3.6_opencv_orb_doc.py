import os
import cv2 as cv
import matplotlib
from matplotlib import pyplot as plt

matplotlib.use('TkAgg')


file_root = f'src_process/test_/'
png_1_path = os.path.join(file_root, 'p-lines-1.jpg')
png_2_path = os.path.join(file_root, 'p-lines-2.jpg')
png_key_1_path = os.path.join(file_root, 'doc_key_1.png')
png_key_2_path = os.path.join(file_root, 'doc_key_2.png')
png_over_path = os.path.join(file_root, 'doc_over.png')

img1 = cv.imread(png_1_path, cv.IMREAD_GRAYSCALE)
img2 = cv.imread(png_2_path, cv.IMREAD_GRAYSCALE)

# Initiate ORB detector
orb = cv.ORB_create()

# find the keypoints and descriptors with ORB
kp1, des1 = orb.detectAndCompute(img1, None)
kp2, des2 = orb.detectAndCompute(img2, None)
# create BFMatcher object
bf = cv.BFMatcher(cv.NORM_HAMMING, crossCheck=True)

# Match descriptors.
matches = bf.match(des1, des2)

# Sort them in the order of their distance.
matches = sorted(matches, key=lambda x: x.distance)

# Draw first 10 matches.
img3 = cv.drawMatches(img1, kp1, img2, kp2, matches[:100], None, flags=cv.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS)

# plt.imshow(img3), plt.show()

cv.imwrite(png_over_path, img3)


