// Stellarium 脚本：显示FIT文件UTC时间信息
// 自动生成于: 2025-08-31 17:24:47
// 显示模式：所有标签绿色显示，按UTC时间顺序出现
// 时间尺度：1秒 = 10分钟实际时间
// 数据来源：日志文件分析结果

LabelMgr.deleteAllLabels();

// 绿色标签颜色
var greenColor = "#00ff00";

// FIT文件UTC时间数据（按时间排序）
var fitData = [
    {
        filename: "GY1_K052-1_No Filter_60S_Bin2_UTC20250826_151830_-15C_.fit",
        region: "K052-1",
        system: "GY1",
        ra: "17.69310h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:18:30",
        startTime: "2025-08-26 17:00:02,494",
        endTime: "2025-08-26 17:30:09,801",
        relativeTime: 0
    },
    {
        filename: "GY1_K052-2_No Filter_60S_Bin2_UTC20250826_152021_-14.9C_.fit",
        region: "K052-2",
        system: "GY1",
        ra: "18.10690h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:20:21",
        startTime: "2025-08-26 17:00:02,500",
        endTime: "2025-08-26 17:30:09,738",
        relativeTime: 0
    },
    {
        filename: "GY1_K052-3_No Filter_60S_Bin2_UTC20250826_152212_-15C_.fit",
        region: "K052-3",
        system: "GY1",
        ra: "18.52069h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:22:12",
        startTime: "2025-08-26 17:00:02,516",
        endTime: "2025-08-26 17:04:49,368",
        relativeTime: 0
    },
    {
        filename: "GY1_K052-4_No Filter_60S_Bin2_UTC20250826_152406_-14.9C_.fit",
        region: "K052-4",
        system: "GY1",
        ra: "17.58421h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:24:06",
        startTime: "2025-08-26 17:00:02,521",
        endTime: "2025-08-26 17:30:09,685",
        relativeTime: 0
    },
    {
        filename: "GY1_K052-5_No Filter_60S_Bin2_UTC20250826_152555_-15C_.fit",
        region: "K052-5",
        system: "GY1",
        ra: "18.00526h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:25:55",
        startTime: "2025-08-26 16:00:02,576",
        endTime: "2025-08-26 16:36:22,453",
        relativeTime: 0
    },
    {
        filename: "GY1_K052-6_No Filter_60S_Bin2_UTC20250826_152746_-15C_.fit",
        region: "K052-6",
        system: "GY1",
        ra: "18.42632h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:27:46",
        startTime: "2025-08-26 17:00:02,526",
        endTime: "2025-08-26 17:30:09,832",
        relativeTime: 0
    },
    {
        filename: "GY2_K052-1_No Filter_60S_Bin2_UTC20250826_151830_-15C_.fit",
        region: "K052-1",
        system: "GY2",
        ra: "17.89310h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:18:30",
        startTime: "2025-08-26 17:00:02,832",
        endTime: "2025-08-26 17:30:10,432",
        relativeTime: 0
    },
    {
        filename: "GY2_K052-2_No Filter_60S_Bin2_UTC20250826_152021_-15C_.fit",
        region: "K052-2",
        system: "GY2",
        ra: "18.30690h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:20:21",
        startTime: "2025-08-26 17:00:02,849",
        endTime: "2025-08-26 17:04:32,697",
        relativeTime: 0
    },
    {
        filename: "GY2_K052-3_No Filter_60S_Bin2_UTC20250826_152211_-14.9C_.fit",
        region: "K052-3",
        system: "GY2",
        ra: "18.72069h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:22:11",
        startTime: "2025-08-26 17:00:02,880",
        endTime: "2025-08-26 17:04:18,795",
        relativeTime: 0
    },
    {
        filename: "GY2_K052-4_No Filter_60S_Bin2_UTC20250826_152406_-15C_.fit",
        region: "K052-4",
        system: "GY2",
        ra: "17.78421h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:24:06",
        startTime: "2025-08-26 17:00:02,888",
        endTime: "2025-08-26 17:30:10,394",
        relativeTime: 0
    },
    {
        filename: "GY2_K052-5_No Filter_60S_Bin2_UTC20250826_152555_-15C_.fit",
        region: "K052-5",
        system: "GY2",
        ra: "18.20526h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:25:55",
        startTime: "2025-08-26 17:00:02,897",
        endTime: "2025-08-26 17:30:10,525",
        relativeTime: 0
    },
    {
        filename: "GY2_K052-6_No Filter_60S_Bin2_UTC20250826_152745_-15C_.fit",
        region: "K052-6",
        system: "GY2",
        ra: "18.62632h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:27:45",
        startTime: "2025-08-26 17:00:02,905",
        endTime: "2025-08-26 17:04:05,798",
        relativeTime: 0
    },
    {
        filename: "GY3_K052-1_No Filter_60S_Bin2_UTC20250826_151831_-14.9C_.fit",
        region: "K052-1",
        system: "GY3",
        ra: "17.69310h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:18:31",
        startTime: "2025-08-26 17:00:30,029",
        endTime: "2025-08-26 17:00:35,358",
        relativeTime: 0
    },
    {
        filename: "GY3_K052-2_No Filter_60S_Bin2_UTC20250826_152021_-15C_.fit",
        region: "K052-2",
        system: "GY3",
        ra: "18.10690h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:20:21",
        startTime: "2025-08-26 17:00:30,417",
        endTime: "2025-08-26 17:30:38,993",
        relativeTime: 0
    },
    {
        filename: "GY3_K052-3_No Filter_60S_Bin2_UTC20250826_152212_-15C_.fit",
        region: "K052-3",
        system: "GY3",
        ra: "18.52069h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:22:12",
        startTime: "2025-08-26 17:00:35,360",
        endTime: "2025-08-26 17:04:34,151",
        relativeTime: 0
    },
    {
        filename: "GY3_K052-4_No Filter_60S_Bin2_UTC20250826_152406_-14.9C_.fit",
        region: "K052-4",
        system: "GY3",
        ra: "17.58421h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:24:06",
        startTime: "2025-08-26 16:00:02,876",
        endTime: "2025-08-26 16:28:00,548",
        relativeTime: 0
    },
    {
        filename: "GY3_K052-5_No Filter_60S_Bin2_UTC20250826_152556_-14.9C_.fit",
        region: "K052-5",
        system: "GY3",
        ra: "18.00526h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:25:56",
        startTime: "2025-08-26 16:00:02,884",
        endTime: "2025-08-26 16:32:59,939",
        relativeTime: 0
    },
    {
        filename: "GY3_K052-6_No Filter_60S_Bin2_UTC20250826_152745_-15C_.fit",
        region: "K052-6",
        system: "GY3",
        ra: "18.42632h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:27:45",
        startTime: "2025-08-26 17:02:47,225",
        endTime: "2025-08-26 17:02:53,628",
        relativeTime: 0
    },
    {
        filename: "GY4_K052-1_No Filter_60S_Bin2_UTC20250826_151831_-15C_.fit",
        region: "K052-1",
        system: "GY4",
        ra: "17.89310h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:18:31",
        startTime: "2025-08-26 16:00:11,223",
        endTime: "2025-08-26 16:35:56,626",
        relativeTime: 0
    },
    {
        filename: "GY4_K052-2_No Filter_60S_Bin2_UTC20250826_152022_-14.9C_.fit",
        region: "K052-2",
        system: "GY4",
        ra: "18.30690h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:20:22",
        startTime: "2025-08-26 17:06:10,423",
        endTime: "2025-08-26 17:06:17,869",
        relativeTime: 0
    },
    {
        filename: "GY4_K052-3_No Filter_60S_Bin2_UTC20250826_152211_-14.9C_.fit",
        region: "K052-3",
        system: "GY4",
        ra: "18.72069h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:22:11",
        startTime: "2025-08-26 17:06:17,872",
        endTime: "2025-08-26 17:10:18,806",
        relativeTime: 0
    },
    {
        filename: "GY4_K052-4_No Filter_60S_Bin2_UTC20250826_152405_-14.9C_.fit",
        region: "K052-4",
        system: "GY4",
        ra: "17.78421h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:24:05",
        startTime: "2025-08-26 17:07:05,322",
        endTime: "2025-08-26 17:37:11,310",
        relativeTime: 0
    },
    {
        filename: "GY4_K052-5_No Filter_60S_Bin2_UTC20250826_152556_-15C_.fit",
        region: "K052-5",
        system: "GY4",
        ra: "18.20526h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:25:56",
        startTime: "2025-08-26 17:07:11,866",
        endTime: "2025-08-26 17:07:23,522",
        relativeTime: 0
    },
    {
        filename: "GY4_K052-6_No Filter_60S_Bin2_UTC20250826_152746_-15C_.fit",
        region: "K052-6",
        system: "GY4",
        ra: "18.62632h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:27:46",
        startTime: "2025-08-26 17:07:23,526",
        endTime: "2025-08-26 17:11:32,654",
        relativeTime: 0
    },
    {
        filename: "GY5_K052-1_No Filter_60S_Bin2_UTC20250826_151831_-15C_.fit",
        region: "K052-1",
        system: "GY5",
        ra: "17.69310h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:18:31",
        startTime: "2025-08-26 16:00:13,000",
        endTime: "2025-08-26 16:23:50,037",
        relativeTime: 0
    },
    {
        filename: "GY5_K052-2_No Filter_60S_Bin2_UTC20250826_152022_-15C_.fit",
        region: "K052-2",
        system: "GY5",
        ra: "18.10690h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:20:22",
        startTime: "2025-08-26 16:00:13,805",
        endTime: "2025-08-26 16:31:26,682",
        relativeTime: 0
    },
    {
        filename: "GY5_K052-3_No Filter_60S_Bin2_UTC20250826_152212_-15C_.fit",
        region: "K052-3",
        system: "GY5",
        ra: "18.52069h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:22:12",
        startTime: "2025-08-26 16:00:14,683",
        endTime: "2025-08-26 16:33:36,169",
        relativeTime: 0
    },
    {
        filename: "GY5_K052-4_No Filter_60S_Bin2_UTC20250826_152406_-14.9C_.fit",
        region: "K052-4",
        system: "GY5",
        ra: "17.58421h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:24:06",
        startTime: "2025-08-26 17:19:54,506",
        endTime: "2025-08-26 17:20:11,540",
        relativeTime: 0
    },
    {
        filename: "GY5_K052-5_No Filter_60S_Bin2_UTC20250826_152556_-14.9C_.fit",
        region: "K052-5",
        system: "GY5",
        ra: "18.00526h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:25:56",
        startTime: "2025-08-26 16:00:20,483",
        endTime: "2025-08-26 16:35:24,651",
        relativeTime: 0
    },
    {
        filename: "GY5_K052-6_No Filter_60S_Bin2_UTC20250826_152745_-15C_.fit",
        region: "K052-6",
        system: "GY5",
        ra: "18.42632h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:27:45",
        startTime: "2025-08-26 17:19:56,619",
        endTime: "2025-08-26 17:50:02,502",
        relativeTime: 0
    },
    {
        filename: "GY6_K052-1_No Filter_60S_Bin2_UTC20250826_151831_-15C_.fit",
        region: "K052-1",
        system: "GY6",
        ra: "17.89310h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:18:31",
        startTime: "2025-08-26 17:34:14,714",
        endTime: "2025-08-26 18:04:19,999",
        relativeTime: 0
    },
    {
        filename: "GY6_K052-2_No Filter_60S_Bin2_UTC20250826_152021_-15C_.fit",
        region: "K052-2",
        system: "GY6",
        ra: "18.30690h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:20:21",
        startTime: "2025-08-26 17:34:15,245",
        endTime: "2025-08-26 18:04:20,817",
        relativeTime: 0
    },
    {
        filename: "GY6_K052-3_No Filter_60S_Bin2_UTC20250826_152212_-15C_.fit",
        region: "K052-3",
        system: "GY6",
        ra: "18.72069h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:22:12",
        startTime: "2025-08-26 17:34:16,697",
        endTime: "2025-08-26 17:39:19,613",
        relativeTime: 0
    },
    {
        filename: "GY6_K052-4_No Filter_60S_Bin2_UTC20250826_152406_-15C_.fit",
        region: "K052-4",
        system: "GY6",
        ra: "17.78421h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:24:06",
        startTime: "2025-08-26 16:00:48,436",
        endTime: "2025-08-26 16:35:04,438",
        relativeTime: 0
    },
    {
        filename: "GY6_K052-5_No Filter_60S_Bin2_UTC20250826_152556_-15C_.fit",
        region: "K052-5",
        system: "GY6",
        ra: "18.20526h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:25:56",
        startTime: "2025-08-26 17:34:18,042",
        endTime: "2025-08-26 18:04:24,497",
        relativeTime: 0
    },
    {
        filename: "GY6_K052-6_No Filter_60S_Bin2_UTC20250826_152745_-15C_.fit",
        region: "K052-6",
        system: "GY6",
        ra: "18.62632h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:27:45",
        startTime: "2025-08-26 17:34:18,641",
        endTime: "2025-08-26 17:39:12,697",
        relativeTime: 0
    },
    {
        filename: "GY1_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-15C_.fit",
        region: "K052-1",
        system: "GY1",
        ra: "17.69310h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: "2025-08-26 17:00:02,511",
        endTime: "2025-08-26 17:30:09,623",
        relativeTime: 1
    },
    {
        filename: "GY1_K052-2_No Filter_60S_Bin2_UTC20250826_153703_-15C_.fit",
        region: "K052-2",
        system: "GY1",
        ra: "18.10690h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:37:03",
        startTime: "2025-08-26 17:00:02,542",
        endTime: "2025-08-26 17:30:09,889",
        relativeTime: 1
    },
    {
        filename: "GY1_K052-7_No Filter_60S_Bin2_UTC20250826_152940_-15C_.fit",
        region: "K052-7",
        system: "GY1",
        ra: "17.67778h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:29:40",
        startTime: "2025-08-26 17:00:02,531",
        endTime: "2025-08-26 17:30:09,664",
        relativeTime: 1
    },
    {
        filename: "GY1_K052-8_No Filter_60S_Bin2_UTC20250826_153130_-15C_.fit",
        region: "K052-8",
        system: "GY1",
        ra: "18.12222h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:31:30",
        startTime: "2025-08-26 16:00:02,586",
        endTime: "2025-08-26 16:35:01,691",
        relativeTime: 1
    },
    {
        filename: "GY1_K052-9_No Filter_60S_Bin2_UTC20250826_153320_-15C_.fit",
        region: "K052-9",
        system: "GY1",
        ra: "18.56667h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:33:20",
        startTime: "2025-08-26 17:00:02,536",
        endTime: "2025-08-26 17:30:09,872",
        relativeTime: 1
    },
    {
        filename: "GY2_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-14.9C_.fit",
        region: "K052-1",
        system: "GY2",
        ra: "17.89310h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: "2025-08-26 17:00:02,873",
        endTime: "2025-08-26 17:30:10,810",
        relativeTime: 1
    },
    {
        filename: "GY2_K052-2_No Filter_60S_Bin2_UTC20250826_153702_-15C_.fit",
        region: "K052-2",
        system: "GY2",
        ra: "18.30690h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:37:02",
        startTime: "2025-08-26 17:00:02,921",
        endTime: "2025-08-26 17:04:30,031",
        relativeTime: 1
    },
    {
        filename: "GY2_K052-7_No Filter_60S_Bin2_UTC20250826_152941_-15C_.fit",
        region: "K052-7",
        system: "GY2",
        ra: "17.87778h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:29:41",
        startTime: "2025-08-26 16:00:02,707",
        endTime: "2025-08-26 16:26:31,377",
        relativeTime: 1
    },
    {
        filename: "GY2_K052-8_No Filter_60S_Bin2_UTC20250826_153129_-15C_.fit",
        region: "K052-8",
        system: "GY2",
        ra: "18.32222h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:31:29",
        startTime: "2025-08-26 16:00:02,710",
        endTime: "2025-08-26 16:30:06,506",
        relativeTime: 1
    },
    {
        filename: "GY2_K052-9_No Filter_60S_Bin2_UTC20250826_153320_-15C_.fit",
        region: "K052-9",
        system: "GY2",
        ra: "18.76667h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:33:20",
        startTime: "2025-08-26 17:00:02,914",
        endTime: "2025-08-26 17:04:14,890",
        relativeTime: 1
    },
    {
        filename: "GY3_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-15C_.fit",
        region: "K052-1",
        system: "GY3",
        ra: "17.69310h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: "2025-08-26 17:00:35,808",
        endTime: "2025-08-26 17:00:43,759",
        relativeTime: 1
    },
    {
        filename: "GY3_K052-2_No Filter_60S_Bin2_UTC20250826_153703_-15C_.fit",
        region: "K052-2",
        system: "GY3",
        ra: "18.10690h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:37:03",
        startTime: "2025-08-26 17:00:43,762",
        endTime: "2025-08-26 17:30:51,691",
        relativeTime: 1
    },
    {
        filename: "GY3_K052-7_No Filter_60S_Bin2_UTC20250826_152940_-14.9C_.fit",
        region: "K052-7",
        system: "GY3",
        ra: "17.67778h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:29:40",
        startTime: "2025-08-26 16:00:02,935",
        endTime: "2025-08-26 16:34:55,645",
        relativeTime: 1
    },
    {
        filename: "GY3_K052-8_No Filter_60S_Bin2_UTC20250826_153130_-14.9C_.fit",
        region: "K052-8",
        system: "GY3",
        ra: "18.12222h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:31:30",
        startTime: "2025-08-26 16:00:02,944",
        endTime: "2025-08-26 16:31:54,291",
        relativeTime: 1
    },
    {
        filename: "GY3_K052-9_No Filter_60S_Bin2_UTC20250826_153319_-15C_.fit",
        region: "K052-9",
        system: "GY3",
        ra: "18.56667h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:33:19",
        startTime: "2025-08-26 17:02:53,629",
        endTime: "2025-08-26 17:02:58,153",
        relativeTime: 1
    },
    {
        filename: "GY4_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-15C_.fit",
        region: "K052-1",
        system: "GY4",
        ra: "17.89310h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: "2025-08-26 16:00:11,405",
        endTime: "2025-08-26 16:35:18,922",
        relativeTime: 1
    },
    {
        filename: "GY4_K052-2_No Filter_60S_Bin2_UTC20250826_153703_-15C_.fit",
        region: "K052-2",
        system: "GY4",
        ra: "18.30690h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:37:03",
        startTime: "2025-08-26 17:07:43,012",
        endTime: "2025-08-26 17:07:52,710",
        relativeTime: 1
    },
    {
        filename: "GY4_K052-7_No Filter_60S_Bin2_UTC20250826_152940_-14.9C_.fit",
        region: "K052-7",
        system: "GY4",
        ra: "17.87778h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:29:40",
        startTime: "2025-08-26 17:07:39,566",
        endTime: "2025-08-26 17:37:46,263",
        relativeTime: 1
    },
    {
        filename: "GY4_K052-8_No Filter_60S_Bin2_UTC20250826_153129_-15C_.fit",
        region: "K052-8",
        system: "GY4",
        ra: "18.32222h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:31:29",
        startTime: "2025-08-26 17:07:56,361",
        endTime: "2025-08-26 17:08:08,108",
        relativeTime: 1
    },
    {
        filename: "GY4_K052-9_No Filter_60S_Bin2_UTC20250826_153320_-14.9C_.fit",
        region: "K052-9",
        system: "GY4",
        ra: "18.76667h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:33:20",
        startTime: "2025-08-26 17:07:57,629",
        endTime: "2025-08-26 17:12:24,468",
        relativeTime: 1
    },
    {
        filename: "GY5_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-14.9C_.fit",
        region: "K052-1",
        system: "GY5",
        ra: "17.69310h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: "2025-08-26 16:00:21,948",
        endTime: "2025-08-26 16:24:47,083",
        relativeTime: 1
    },
    {
        filename: "GY5_K052-2_No Filter_60S_Bin2_UTC20250826_153703_-15C_.fit",
        region: "K052-2",
        system: "GY5",
        ra: "18.10690h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:37:03",
        startTime: "2025-08-26 17:20:11,546",
        endTime: "2025-08-26 17:50:19,354",
        relativeTime: 1
    },
    {
        filename: "GY5_K052-7_No Filter_60S_Bin2_UTC20250826_152941_-15C_.fit",
        region: "K052-7",
        system: "GY5",
        ra: "17.67778h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:29:41",
        startTime: "2025-08-26 16:00:21,073",
        endTime: "2025-08-26 16:21:53,126",
        relativeTime: 1
    },
    {
        filename: "GY5_K052-8_No Filter_60S_Bin2_UTC20250826_153130_-15C_.fit",
        region: "K052-8",
        system: "GY5",
        ra: "18.12222h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:31:30",
        startTime: "2025-08-26 16:00:21,838",
        endTime: "2025-08-26 16:35:59,088",
        relativeTime: 1
    },
    {
        filename: "GY5_K052-9_No Filter_60S_Bin2_UTC20250826_153319_-14.9C_.fit",
        region: "K052-9",
        system: "GY5",
        ra: "18.56667h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:33:19",
        startTime: "2025-08-26 17:30:09,717",
        endTime: "2025-08-26 18:00:16,063",
        relativeTime: 1
    },
    {
        filename: "GY6_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-15C_.fit",
        region: "K052-1",
        system: "GY6",
        ra: "17.89310h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: "2025-08-26 17:34:16,512",
        endTime: "2025-08-26 18:02:01,466",
        relativeTime: 1
    },
    {
        filename: "GY6_K052-2_No Filter_60S_Bin2_UTC20250826_153703_-15C_.fit",
        region: "K052-2",
        system: "GY6",
        ra: "18.30690h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:37:03",
        startTime: "2025-08-26 16:02:31,914",
        endTime: "2025-08-26 16:38:59,275",
        relativeTime: 1
    },
    {
        filename: "GY6_K052-7_No Filter_60S_Bin2_UTC20250826_152940_-15C_.fit",
        region: "K052-7",
        system: "GY6",
        ra: "17.87778h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:29:40",
        startTime: "2025-08-26 16:01:43,669",
        endTime: "2025-08-26 16:31:21,699",
        relativeTime: 1
    },
    {
        filename: "GY6_K052-8_No Filter_60S_Bin2_UTC20250826_153129_-14.9C_.fit",
        region: "K052-8",
        system: "GY6",
        ra: "18.32222h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:31:29",
        startTime: "2025-08-26 16:02:10,508",
        endTime: "2025-08-26 16:35:49,557",
        relativeTime: 1
    },
    {
        filename: "GY6_K052-9_No Filter_60S_Bin2_UTC20250826_153320_-15C_.fit",
        region: "K052-9",
        system: "GY6",
        ra: "18.76667h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:33:20",
        startTime: "2025-08-26 16:02:29,053",
        endTime: "2025-08-26 16:39:01,456",
        relativeTime: 1
    },
    {
        filename: "GY1_K052-3_No Filter_60S_Bin2_UTC20250826_153853_-15C_.fit",
        region: "K052-3",
        system: "GY1",
        ra: "18.52069h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:38:53",
        startTime: "2025-08-26 17:00:02,547",
        endTime: "2025-08-26 17:04:46,146",
        relativeTime: 2
    },
    {
        filename: "GY1_K052-4_No Filter_60S_Bin2_UTC20250826_154049_-15C_.fit",
        region: "K052-4",
        system: "GY1",
        ra: "17.58421h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:40:49",
        startTime: "2025-08-26 17:00:02,551",
        endTime: "2025-08-26 17:30:09,754",
        relativeTime: 2
    },
    {
        filename: "GY1_K052-5_No Filter_60S_Bin2_UTC20250826_154238_-15C_.fit",
        region: "K052-5",
        system: "GY1",
        ra: "18.00526h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:42:38",
        startTime: "2025-08-26 16:00:02,602",
        endTime: "2025-08-26 16:36:32,105",
        relativeTime: 2
    },
    {
        filename: "GY1_K052-6_No Filter_60S_Bin2_UTC20250826_154426_-15C_.fit",
        region: "K052-6",
        system: "GY1",
        ra: "18.42632h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:44:26",
        startTime: "2025-08-26 17:00:02,557",
        endTime: "2025-08-26 17:30:10,866",
        relativeTime: 2
    },
    {
        filename: "GY1_K052-7_No Filter_60S_Bin2_UTC20250826_154621_-15C_.fit",
        region: "K052-7",
        system: "GY1",
        ra: "17.67778h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:46:21",
        startTime: "2025-08-26 16:00:02,608",
        endTime: "2025-08-26 16:34:18,302",
        relativeTime: 2
    },
    {
        filename: "GY1_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-14.9C_.fit",
        region: "K052-8",
        system: "GY1",
        ra: "18.12222h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: "2025-08-26 16:00:02,612",
        endTime: "2025-08-26 16:31:35,709",
        relativeTime: 2
    },
    {
        filename: "GY2_K052-3_No Filter_60S_Bin2_UTC20250826_153853_-15C_.fit",
        region: "K052-3",
        system: "GY2",
        ra: "18.72069h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:38:53",
        startTime: "2025-08-26 17:00:02,929",
        endTime: "2025-08-26 17:03:56,257",
        relativeTime: 2
    },
    {
        filename: "GY2_K052-4_No Filter_60S_Bin2_UTC20250826_154048_-15C_.fit",
        region: "K052-4",
        system: "GY2",
        ra: "17.78421h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:40:48",
        startTime: "2025-08-26 17:00:02,938",
        endTime: "2025-08-26 17:33:30,172",
        relativeTime: 2
    },
    {
        filename: "GY2_K052-5_No Filter_60S_Bin2_UTC20250826_154237_-14.9C_.fit",
        region: "K052-5",
        system: "GY2",
        ra: "18.20526h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:42:37",
        startTime: "2025-08-26 16:00:02,727",
        endTime: "2025-08-26 16:32:27,583",
        relativeTime: 2
    },
    {
        filename: "GY2_K052-6_No Filter_60S_Bin2_UTC20250826_154427_-15C_.fit",
        region: "K052-6",
        system: "GY2",
        ra: "18.62632h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:44:27",
        startTime: "2025-08-26 17:00:02,945",
        endTime: "2025-08-26 17:03:55,716",
        relativeTime: 2
    },
    {
        filename: "GY2_K052-7_No Filter_60S_Bin2_UTC20250826_154621_-14.9C_.fit",
        region: "K052-7",
        system: "GY2",
        ra: "17.87778h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:46:21",
        startTime: "2025-08-26 16:00:02,734",
        endTime: "2025-08-26 16:25:53,169",
        relativeTime: 2
    },
    {
        filename: "GY2_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-14.9C_.fit",
        region: "K052-8",
        system: "GY2",
        ra: "18.32222h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: "2025-08-26 16:00:02,737",
        endTime: "2025-08-26 16:31:33,237",
        relativeTime: 2
    },
    {
        filename: "GY3_K052-3_No Filter_60S_Bin2_UTC20250826_153854_-15C_.fit",
        region: "K052-3",
        system: "GY3",
        ra: "18.52069h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:38:54",
        startTime: "2025-08-26 17:02:01,728",
        endTime: "2025-08-26 17:05:19,048",
        relativeTime: 2
    },
    {
        filename: "GY3_K052-4_No Filter_60S_Bin2_UTC20250826_154048_-15C_.fit",
        region: "K052-4",
        system: "GY3",
        ra: "17.58421h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:40:48",
        startTime: "2025-08-26 16:00:02,918",
        endTime: "2025-08-26 16:28:03,157",
        relativeTime: 2
    },
    {
        filename: "GY3_K052-5_No Filter_60S_Bin2_UTC20250826_154237_-15C_.fit",
        region: "K052-5",
        system: "GY3",
        ra: "18.00526h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:42:37",
        startTime: "2025-08-26 17:03:02,673",
        endTime: "2025-08-26 17:34:26,892",
        relativeTime: 2
    },
    {
        filename: "GY3_K052-6_No Filter_60S_Bin2_UTC20250826_154426_-15C_.fit",
        region: "K052-6",
        system: "GY3",
        ra: "18.42632h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:44:26",
        startTime: "2025-08-26 17:03:03,519",
        endTime: "2025-08-26 17:03:10,715",
        relativeTime: 2
    },
    {
        filename: "GY3_K052-7_No Filter_60S_Bin2_UTC20250826_154621_-14.9C_.fit",
        region: "K052-7",
        system: "GY3",
        ra: "17.67778h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:46:21",
        startTime: "2025-08-26 16:00:02,996",
        endTime: "2025-08-26 16:35:09,366",
        relativeTime: 2
    },
    {
        filename: "GY3_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-15C_.fit",
        region: "K052-8",
        system: "GY3",
        ra: "18.12222h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: "2025-08-26 17:03:04,315",
        endTime: "2025-08-26 17:33:57,994",
        relativeTime: 2
    },
    {
        filename: "GY4_K052-3_No Filter_60S_Bin2_UTC20250826_153854_-15C_.fit",
        region: "K052-3",
        system: "GY4",
        ra: "18.72069h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:38:54",
        startTime: "2025-08-26 17:07:47,712",
        endTime: "2025-08-26 17:12:51,029",
        relativeTime: 2
    },
    {
        filename: "GY4_K052-4_No Filter_60S_Bin2_UTC20250826_154048_-15C_.fit",
        region: "K052-4",
        system: "GY4",
        ra: "17.78421h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:40:48",
        startTime: "2025-08-26 17:07:49,160",
        endTime: "2025-08-26 17:37:55,012",
        relativeTime: 2
    },
    {
        filename: "GY4_K052-5_No Filter_60S_Bin2_UTC20250826_154237_-14.9C_.fit",
        region: "K052-5",
        system: "GY4",
        ra: "18.20526h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:42:37",
        startTime: "2025-08-26 17:07:52,714",
        endTime: "2025-08-26 17:08:01,843",
        relativeTime: 2
    },
    {
        filename: "GY4_K052-6_No Filter_60S_Bin2_UTC20250826_154427_-15C_.fit",
        region: "K052-6",
        system: "GY4",
        ra: "18.62632h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:44:27",
        startTime: "2025-08-26 17:07:54,343",
        endTime: "2025-08-26 17:38:01,483",
        relativeTime: 2
    },
    {
        filename: "GY4_K052-7_No Filter_60S_Bin2_UTC20250826_154621_-15C_.fit",
        region: "K052-7",
        system: "GY4",
        ra: "17.87778h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:46:21",
        startTime: "2025-08-26 17:08:44,151",
        endTime: "2025-08-26 17:43:34,597",
        relativeTime: 2
    },
    {
        filename: "GY4_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-14.9C_.fit",
        region: "K052-8",
        system: "GY4",
        ra: "18.32222h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: "2025-08-26 17:08:44,596",
        endTime: "2025-08-26 17:14:18,153",
        relativeTime: 2
    },
    {
        filename: "GY5_K052-3_No Filter_60S_Bin2_UTC20250826_153854_-15C_.fit",
        region: "K052-3",
        system: "GY5",
        ra: "18.52069h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:38:54",
        startTime: "2025-08-26 17:20:30,054",
        endTime: "2025-08-26 17:50:38,878",
        relativeTime: 2
    },
    {
        filename: "GY5_K052-4_No Filter_60S_Bin2_UTC20250826_154048_-15C_.fit",
        region: "K052-4",
        system: "GY5",
        ra: "17.58421h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:40:48",
        startTime: "2025-08-26 17:22:56,264",
        endTime: "2025-08-26 17:23:12,261",
        relativeTime: 2
    },
    {
        filename: "GY5_K052-5_No Filter_60S_Bin2_UTC20250826_154236_-14.9C_.fit",
        region: "K052-5",
        system: "GY5",
        ra: "18.00526h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:42:36",
        startTime: "2025-08-26 16:00:22,220",
        endTime: "2025-08-26 16:34:17,025",
        relativeTime: 2
    },
    {
        filename: "GY5_K052-6_No Filter_60S_Bin2_UTC20250826_154427_-15C_.fit",
        region: "K052-6",
        system: "GY5",
        ra: "18.42632h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:44:27",
        startTime: "2025-08-26 17:23:12,266",
        endTime: "2025-08-26 17:53:18,427",
        relativeTime: 2
    },
    {
        filename: "GY5_K052-7_No Filter_60S_Bin2_UTC20250826_154621_-15C_.fit",
        region: "K052-7",
        system: "GY5",
        ra: "17.67778h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:46:21",
        startTime: "2025-08-26 16:00:22,313",
        endTime: "2025-08-26 16:34:08,654",
        relativeTime: 2
    },
    {
        filename: "GY5_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-15C_.fit",
        region: "K052-8",
        system: "GY5",
        ra: "18.12222h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: "2025-08-26 17:26:17,028",
        endTime: "2025-08-26 17:53:17,205",
        relativeTime: 2
    },
    {
        filename: "GY6_K052-3_No Filter_60S_Bin2_UTC20250826_153853_-14.9C_.fit",
        region: "K052-3",
        system: "GY6",
        ra: "18.72069h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:38:53",
        startTime: "2025-08-26 17:34:19,157",
        endTime: "2025-08-26 17:39:09,709",
        relativeTime: 2
    },
    {
        filename: "GY6_K052-4_No Filter_60S_Bin2_UTC20250826_154049_-15C_.fit",
        region: "K052-4",
        system: "GY6",
        ra: "17.78421h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:40:49",
        startTime: "2025-08-26 16:03:04,436",
        endTime: "2025-08-26 16:29:55,707",
        relativeTime: 2
    },
    {
        filename: "GY6_K052-5_No Filter_60S_Bin2_UTC20250826_154237_-15C_.fit",
        region: "K052-5",
        system: "GY6",
        ra: "18.20526h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:42:37",
        startTime: "2025-08-26 16:03:16,110",
        endTime: "2025-08-26 16:36:47,789",
        relativeTime: 2
    },
    {
        filename: "GY6_K052-6_No Filter_60S_Bin2_UTC20250826_154426_-15C_.fit",
        region: "K052-6",
        system: "GY6",
        ra: "18.62632h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:44:26",
        startTime: "2025-08-26 17:34:19,452",
        endTime: "2025-08-26 17:34:29,295",
        relativeTime: 2
    },
    {
        filename: "GY6_K052-7_No Filter_60S_Bin2_UTC20250826_154622_-14.9C_.fit",
        region: "K052-7",
        system: "GY6",
        ra: "17.87778h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:46:22",
        startTime: "2025-08-26 16:03:45,009",
        endTime: "2025-08-26 16:30:31,469",
        relativeTime: 2
    },
    {
        filename: "GY6_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-14.9C_.fit",
        region: "K052-8",
        system: "GY6",
        ra: "18.32222h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: "2025-08-26 16:03:46,084",
        endTime: "2025-08-26 16:35:46,160",
        relativeTime: 2
    },
    {
        filename: "GY1_K052-1_No Filter_60S_Bin2_UTC20250826_155155_-14.9C_.fit",
        region: "K052-1",
        system: "GY1",
        ra: "17.69310h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:51:55",
        startTime: "2025-08-26 17:00:02,566",
        endTime: "2025-08-26 17:34:15,184",
        relativeTime: 3
    },
    {
        filename: "GY1_K052-2_No Filter_60S_Bin2_UTC20250826_155345_-15C_.fit",
        region: "K052-2",
        system: "GY1",
        ra: "18.10690h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:53:45",
        startTime: "2025-08-26 17:00:02,572",
        endTime: "2025-08-26 17:34:57,661",
        relativeTime: 3
    },
    {
        filename: "GY1_K052-3_No Filter_60S_Bin2_UTC20250826_155534_-14.9C_.fit",
        region: "K052-3",
        system: "GY1",
        ra: "18.52069h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:55:34",
        startTime: "2025-08-26 17:00:02,576",
        endTime: "2025-08-26 17:04:48,562",
        relativeTime: 3
    },
    {
        filename: "GY1_K052-4_No Filter_60S_Bin2_UTC20250826_155730_-15C_.fit",
        region: "K052-4",
        system: "GY1",
        ra: "17.58421h",
        dec: "+21.98",
        utcTime: "2025-08-26 15:57:30",
        startTime: "2025-08-26 17:00:02,580",
        endTime: "2025-08-26 17:33:57,787",
        relativeTime: 3
    },
    {
        filename: "GY1_K052-9_No Filter_60S_Bin2_UTC20250826_155001_-14.9C_.fit",
        region: "K052-9",
        system: "GY1",
        ra: "18.56667h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:50:01",
        startTime: "2025-08-26 17:00:02,561",
        endTime: "2025-08-26 17:30:10,655",
        relativeTime: 3
    },
    {
        filename: "GY2_K052-1_No Filter_60S_Bin2_UTC20250826_155155_-15C_.fit",
        region: "K052-1",
        system: "GY2",
        ra: "17.89310h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:51:55",
        startTime: "2025-08-26 17:00:02,952",
        endTime: "2025-08-26 17:33:47,363",
        relativeTime: 3
    },
    {
        filename: "GY2_K052-2_No Filter_60S_Bin2_UTC20250826_155345_-15C_.fit",
        region: "K052-2",
        system: "GY2",
        ra: "18.30690h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:53:45",
        startTime: "2025-08-26 17:00:02,961",
        endTime: "2025-08-26 17:03:50,769",
        relativeTime: 3
    },
    {
        filename: "GY2_K052-3_No Filter_60S_Bin2_UTC20250826_155534_-15C_.fit",
        region: "K052-3",
        system: "GY2",
        ra: "18.72069h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:55:34",
        startTime: "2025-08-26 17:00:02,967",
        endTime: "2025-08-26 17:03:59,378",
        relativeTime: 3
    },
    {
        filename: "GY2_K052-4_No Filter_60S_Bin2_UTC20250826_155730_-15C_.fit",
        region: "K052-4",
        system: "GY2",
        ra: "17.78421h",
        dec: "+21.98",
        utcTime: "2025-08-26 15:57:30",
        startTime: "2025-08-26 17:00:02,975",
        endTime: "2025-08-26 17:33:27,083",
        relativeTime: 3
    },
    {
        filename: "GY2_K052-9_No Filter_60S_Bin2_UTC20250826_155000_-15C_.fit",
        region: "K052-9",
        system: "GY2",
        ra: "18.76667h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:50:00",
        startTime: "2025-08-26 17:00:02,949",
        endTime: "2025-08-26 17:04:06,343",
        relativeTime: 3
    },
    {
        filename: "GY3_K052-1_No Filter_60S_Bin2_UTC20250826_155155_-15C_.fit",
        region: "K052-1",
        system: "GY3",
        ra: "17.69310h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:51:55",
        startTime: "2025-08-26 17:02:56,389",
        endTime: "2025-08-26 17:03:03,516",
        relativeTime: 3
    },
    {
        filename: "GY3_K052-2_No Filter_60S_Bin2_UTC20250826_155346_-15C_.fit",
        region: "K052-2",
        system: "GY3",
        ra: "18.10690h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:53:46",
        startTime: "2025-08-26 17:02:58,154",
        endTime: "2025-08-26 17:41:18,103",
        relativeTime: 3
    },
    {
        filename: "GY3_K052-3_No Filter_60S_Bin2_UTC20250826_155534_-14.9C_.fit",
        region: "K052-3",
        system: "GY3",
        ra: "18.52069h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:55:34",
        startTime: "2025-08-26 17:02:59,963",
        endTime: "2025-08-26 17:06:10,420",
        relativeTime: 3
    },
    {
        filename: "GY3_K052-4_No Filter_60S_Bin2_UTC20250826_155730_-15C_.fit",
        region: "K052-4",
        system: "GY3",
        ra: "17.58421h",
        dec: "+20.98",
        utcTime: "2025-08-26 15:57:30",
        startTime: "2025-08-26 17:03:18,031",
        endTime: "2025-08-26 17:36:37,893",
        relativeTime: 3
    },
    {
        filename: "GY3_K052-9_No Filter_60S_Bin2_UTC20250826_155001_-14.9C_.fit",
        region: "K052-9",
        system: "GY3",
        ra: "18.56667h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:50:01",
        startTime: "2025-08-26 17:03:10,717",
        endTime: "2025-08-26 17:03:18,029",
        relativeTime: 3
    },
    {
        filename: "GY4_K052-1_No Filter_60S_Bin2_UTC20250826_155155_-15C_.fit",
        region: "K052-1",
        system: "GY4",
        ra: "17.89310h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:51:55",
        startTime: "2025-08-26 17:08:01,847",
        endTime: "2025-08-26 17:36:58,942",
        relativeTime: 3
    },
    {
        filename: "GY4_K052-2_No Filter_60S_Bin2_UTC20250826_155346_-14.9C_.fit",
        region: "K052-2",
        system: "GY4",
        ra: "18.30690h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:53:46",
        startTime: "2025-08-26 17:08:08,113",
        endTime: "2025-08-26 17:14:27,670",
        relativeTime: 3
    },
    {
        filename: "GY4_K052-3_No Filter_60S_Bin2_UTC20250826_155535_-14.9C_.fit",
        region: "K052-3",
        system: "GY4",
        ra: "18.72069h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:55:35",
        startTime: "2025-08-26 17:08:17,501",
        endTime: "2025-08-26 17:13:03,642",
        relativeTime: 3
    },
    {
        filename: "GY4_K052-4_No Filter_60S_Bin2_UTC20250826_155729_-14.9C_.fit",
        region: "K052-4",
        system: "GY4",
        ra: "17.78421h",
        dec: "+20.98",
        utcTime: "2025-08-26 15:57:29",
        startTime: "2025-08-26 17:08:17,787",
        endTime: "2025-08-26 17:42:34,587",
        relativeTime: 3
    },
    {
        filename: "GY4_K052-9_No Filter_60S_Bin2_UTC20250826_155001_-15C_.fit",
        region: "K052-9",
        system: "GY4",
        ra: "18.76667h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:50:01",
        startTime: "2025-08-26 17:08:48,115",
        endTime: "2025-08-26 17:44:20,967",
        relativeTime: 3
    },
    {
        filename: "GY5_K052-1_No Filter_60S_Bin2_UTC20250826_155156_-15C_.fit",
        region: "K052-1",
        system: "GY5",
        ra: "17.69310h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:51:56",
        startTime: "2025-08-26 17:30:09,719",
        endTime: "2025-08-26 18:04:33,797",
        relativeTime: 3
    },
    {
        filename: "GY5_K052-2_No Filter_60S_Bin2_UTC20250826_155345_-14.9C_.fit",
        region: "K052-2",
        system: "GY5",
        ra: "18.10690h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:53:45",
        startTime: "2025-08-26 17:30:09,721",
        endTime: "2025-08-26 18:04:52,285",
        relativeTime: 3
    },
    {
        filename: "GY5_K052-3_No Filter_60S_Bin2_UTC20250826_155534_-14.9C_.fit",
        region: "K052-3",
        system: "GY5",
        ra: "18.52069h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:55:34",
        startTime: "2025-08-26 17:30:09,764",
        endTime: "2025-08-26 17:34:24,668",
        relativeTime: 3
    },
    {
        filename: "GY5_K052-4_No Filter_60S_Bin2_UTC20250826_155730_-15C_.fit",
        region: "K052-4",
        system: "GY5",
        ra: "17.58421h",
        dec: "+19.98",
        utcTime: "2025-08-26 15:57:30",
        startTime: "2025-08-26 17:30:09,805",
        endTime: "2025-08-26 17:34:12,537",
        relativeTime: 3
    },
    {
        filename: "GY5_K052-9_No Filter_60S_Bin2_UTC20250826_155000_-15C_.fit",
        region: "K052-9",
        system: "GY5",
        ra: "18.56667h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:50:00",
        startTime: "2025-08-26 17:19:38,993",
        endTime: "2025-08-26 17:55:26,060",
        relativeTime: 3
    },
    {
        filename: "GY6_K052-1_No Filter_60S_Bin2_UTC20250826_155155_-15C_.fit",
        region: "K052-1",
        system: "GY6",
        ra: "17.89310h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:51:55",
        startTime: "2025-08-26 17:34:21,632",
        endTime: "2025-08-26 18:07:36,375",
        relativeTime: 3
    },
    {
        filename: "GY6_K052-2_No Filter_60S_Bin2_UTC20250826_155345_-14.9C_.fit",
        region: "K052-2",
        system: "GY6",
        ra: "18.30690h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:53:45",
        startTime: "2025-08-26 17:34:21,836",
        endTime: "2025-08-26 18:11:53,448",
        relativeTime: 3
    },
    {
        filename: "GY6_K052-3_No Filter_60S_Bin2_UTC20250826_155534_-14.9C_.fit",
        region: "K052-3",
        system: "GY6",
        ra: "18.72069h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:55:34",
        startTime: "2025-08-26 17:34:21,858",
        endTime: "2025-08-26 17:38:57,363",
        relativeTime: 3
    },
    {
        filename: "GY6_K052-4_No Filter_60S_Bin2_UTC20250826_155730_-15C_.fit",
        region: "K052-4",
        system: "GY6",
        ra: "17.78421h",
        dec: "+19.98",
        utcTime: "2025-08-26 15:57:30",
        startTime: "2025-08-26 17:34:22,059",
        endTime: "2025-08-26 18:08:24,648",
        relativeTime: 3
    },
    {
        filename: "GY6_K052-9_No Filter_60S_Bin2_UTC20250826_155001_-15C_.fit",
        region: "K052-9",
        system: "GY6",
        ra: "18.76667h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:50:01",
        startTime: "2025-08-26 17:34:19,615",
        endTime: "2025-08-26 18:04:23,718",
        relativeTime: 3
    },
    {
        filename: "GY1_K052-5_No Filter_60S_Bin2_UTC20250826_155919_-14.9C_.fit",
        region: "K052-5",
        system: "GY1",
        ra: "18.00526h",
        dec: "+21.98",
        utcTime: "2025-08-26 15:59:19",
        startTime: "2025-08-26 17:00:02,585",
        endTime: "2025-08-26 17:34:26,251",
        relativeTime: 4
    },
    {
        filename: "GY1_K052-6_No Filter_60S_Bin2_UTC20250826_160108_-14.9C_.fit",
        region: "K052-6",
        system: "GY1",
        ra: "18.42632h",
        dec: "+21.98",
        utcTime: "2025-08-26 16:01:08",
        startTime: "2025-08-26 17:00:02,589",
        endTime: "2025-08-26 17:35:21,143",
        relativeTime: 4
    },
    {
        filename: "GY1_K052-7_No Filter_60S_Bin2_UTC20250826_160304_-15C_.fit",
        region: "K052-7",
        system: "GY1",
        ra: "17.67778h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:03:04",
        startTime: "2025-08-26 17:00:02,497",
        endTime: "2025-08-26 17:34:07,045",
        relativeTime: 4
    },
    {
        filename: "GY1_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-15C_.fit",
        region: "K052-8",
        system: "GY1",
        ra: "18.12222h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: "2025-08-26 17:00:02,491",
        endTime: "2025-08-26 17:34:34,269",
        relativeTime: 4
    },
    {
        filename: "GY1_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY1",
        ra: "18.56667h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: "2025-08-26 17:00:02,506",
        endTime: "2025-08-26 17:36:44,335",
        relativeTime: 4
    },
    {
        filename: "GY2_K052-5_No Filter_60S_Bin2_UTC20250826_155920_-15C_.fit",
        region: "K052-5",
        system: "GY2",
        ra: "18.20526h",
        dec: "+21.98",
        utcTime: "2025-08-26 15:59:20",
        startTime: "2025-08-26 17:00:02,982",
        endTime: "2025-08-26 17:33:56,357",
        relativeTime: 4
    },
    {
        filename: "GY2_K052-6_No Filter_60S_Bin2_UTC20250826_160109_-14.9C_.fit",
        region: "K052-6",
        system: "GY2",
        ra: "18.62632h",
        dec: "+21.98",
        utcTime: "2025-08-26 16:01:09",
        startTime: "2025-08-26 17:00:02,990",
        endTime: "2025-08-26 17:03:41,757",
        relativeTime: 4
    },
    {
        filename: "GY2_K052-7_No Filter_60S_Bin2_UTC20250826_160304_-15C_.fit",
        region: "K052-7",
        system: "GY2",
        ra: "17.87778h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:03:04",
        startTime: "2025-08-26 17:00:02,840",
        endTime: "2025-08-26 17:33:32,526",
        relativeTime: 4
    },
    {
        filename: "GY2_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-14.9C_.fit",
        region: "K052-8",
        system: "GY2",
        ra: "18.32222h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: "2025-08-26 17:00:02,823",
        endTime: "2025-08-26 17:34:13,409",
        relativeTime: 4
    },
    {
        filename: "GY2_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY2",
        ra: "18.76667h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: "2025-08-26 17:00:02,855",
        endTime: "2025-08-26 17:04:34,109",
        relativeTime: 4
    },
    {
        filename: "GY3_K052-5_No Filter_60S_Bin2_UTC20250826_155920_-14.9C_.fit",
        region: "K052-5",
        system: "GY3",
        ra: "18.00526h",
        dec: "+20.98",
        utcTime: "2025-08-26 15:59:20",
        startTime: "2025-08-26 17:03:20,113",
        endTime: "2025-08-26 17:36:56,113",
        relativeTime: 4
    },
    {
        filename: "GY3_K052-6_No Filter_60S_Bin2_UTC20250826_160109_-15C_.fit",
        region: "K052-6",
        system: "GY3",
        ra: "18.42632h",
        dec: "+20.98",
        utcTime: "2025-08-26 16:01:09",
        startTime: "2025-08-26 17:03:21,624",
        endTime: "2025-08-26 17:07:49,156",
        relativeTime: 4
    },
    {
        filename: "GY3_K052-7_No Filter_60S_Bin2_UTC20250826_160305_-15C_.fit",
        region: "K052-7",
        system: "GY3",
        ra: "17.67778h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:03:05",
        startTime: "2025-08-26 17:00:30,032",
        endTime: "2025-08-26 17:34:07,402",
        relativeTime: 4
    },
    {
        filename: "GY3_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-15C_.fit",
        region: "K052-8",
        system: "GY3",
        ra: "18.12222h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: "2025-08-26 17:00:28,356",
        endTime: "2025-08-26 17:34:22,056",
        relativeTime: 4
    },
    {
        filename: "GY3_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY3",
        ra: "18.56667h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: "2025-08-26 17:00:31,570",
        endTime: "2025-08-26 17:05:25,608",
        relativeTime: 4
    },
    {
        filename: "GY4_K052-5_No Filter_60S_Bin2_UTC20250826_155920_-15C_.fit",
        region: "K052-5",
        system: "GY4",
        ra: "18.20526h",
        dec: "+20.98",
        utcTime: "2025-08-26 15:59:20",
        startTime: "2025-08-26 17:08:23,480",
        endTime: "2025-08-26 17:14:26,700",
        relativeTime: 4
    },
    {
        filename: "GY4_K052-6_No Filter_60S_Bin2_UTC20250826_160109_-15C_.fit",
        region: "K052-6",
        system: "GY4",
        ra: "18.62632h",
        dec: "+20.98",
        utcTime: "2025-08-26 16:01:09",
        startTime: "2025-08-26 17:08:49,757",
        endTime: "2025-08-26 17:13:13,987",
        relativeTime: 4
    },
    {
        filename: "GY4_K052-7_No Filter_60S_Bin2_UTC20250826_160304_-15C_.fit",
        region: "K052-7",
        system: "GY4",
        ra: "17.87778h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:03:04",
        startTime: "2025-08-26 17:05:58,842",
        endTime: "2025-08-26 17:40:34,117",
        relativeTime: 4
    },
    {
        filename: "GY4_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-15C_.fit",
        region: "K052-8",
        system: "GY4",
        ra: "18.32222h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: "2025-08-26 17:05:57,630",
        endTime: "2025-08-26 17:09:51,003",
        relativeTime: 4
    },
    {
        filename: "GY4_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY4",
        ra: "18.76667h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: "2025-08-26 17:06:14,327",
        endTime: "2025-08-26 17:10:18,448",
        relativeTime: 4
    },
    {
        filename: "GY5_K052-5_No Filter_60S_Bin2_UTC20250826_155920_-14.9C_.fit",
        region: "K052-5",
        system: "GY5",
        ra: "18.00526h",
        dec: "+19.98",
        utcTime: "2025-08-26 15:59:20",
        startTime: "2025-08-26 17:30:09,830",
        endTime: "2025-08-26 18:05:44,657",
        relativeTime: 4
    },
    {
        filename: "GY5_K052-6_No Filter_60S_Bin2_UTC20250826_160109_-15C_.fit",
        region: "K052-6",
        system: "GY5",
        ra: "18.42632h",
        dec: "+19.98",
        utcTime: "2025-08-26 16:01:09",
        startTime: "2025-08-26 17:30:09,855",
        endTime: "2025-08-26 18:05:05,896",
        relativeTime: 4
    },
    {
        filename: "GY5_K052-7_No Filter_60S_Bin2_UTC20250826_160305_-15C_.fit",
        region: "K052-7",
        system: "GY5",
        ra: "17.67778h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:03:05",
        startTime: "2025-08-26 17:30:09,904",
        endTime: "2025-08-26 18:01:56,736",
        relativeTime: 4
    },
    {
        filename: "GY5_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-15C_.fit",
        region: "K052-8",
        system: "GY5",
        ra: "18.12222h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: "2025-08-26 17:19:31,823",
        endTime: "2025-08-26 17:55:11,691",
        relativeTime: 4
    },
    {
        filename: "GY5_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY5",
        ra: "18.56667h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: "2025-08-26 17:19:39,845",
        endTime: "2025-08-26 17:55:34,871",
        relativeTime: 4
    },
    {
        filename: "GY6_K052-5_No Filter_60S_Bin2_UTC20250826_155920_-14.9C_.fit",
        region: "K052-5",
        system: "GY6",
        ra: "18.20526h",
        dec: "+19.98",
        utcTime: "2025-08-26 15:59:20",
        startTime: "2025-08-26 17:34:24,671",
        endTime: "2025-08-26 18:08:41,616",
        relativeTime: 4
    },
    {
        filename: "GY6_K052-6_No Filter_60S_Bin2_UTC20250826_160109_-15C_.fit",
        region: "K052-6",
        system: "GY6",
        ra: "18.62632h",
        dec: "+19.98",
        utcTime: "2025-08-26 16:01:09",
        startTime: "2025-08-26 17:34:24,895",
        endTime: "2025-08-26 17:39:06,173",
        relativeTime: 4
    },
    {
        filename: "GY6_K052-7_No Filter_60S_Bin2_UTC20250826_160305_-15C_.fit",
        region: "K052-7",
        system: "GY6",
        ra: "17.87778h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:03:05",
        startTime: "2025-08-26 17:34:14,979",
        endTime: "2025-08-26 18:02:04,689",
        relativeTime: 4
    },
    {
        filename: "GY6_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-15C_.fit",
        region: "K052-8",
        system: "GY6",
        ra: "18.32222h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: "2025-08-26 17:34:13,441",
        endTime: "2025-08-26 18:08:40,632",
        relativeTime: 4
    },
    {
        filename: "GY6_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY6",
        ra: "18.76667h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: "2025-08-26 17:34:15,708",
        endTime: "2025-08-26 18:10:23,879",
        relativeTime: 4
    },
    {
        filename: "GY1_K074-1_No Filter_60S_Bin2_UTC20250826_160847_-15C_.fit",
        region: "K074-1",
        system: "GY1",
        ra: "19.50000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:08:47",
        startTime: "2025-08-26 17:00:02,594",
        endTime: "2025-08-26 17:35:06,952",
        relativeTime: 5
    },
    {
        filename: "GY1_K074-2_No Filter_60S_Bin2_UTC20250826_161036_-14.9C_.fit",
        region: "K074-2",
        system: "GY1",
        ra: "19.90000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:10:36",
        startTime: "2025-08-26 17:00:02,597",
        endTime: "2025-08-26 17:34:35,693",
        relativeTime: 5
    },
    {
        filename: "GY1_K074-3_No Filter_60S_Bin2_UTC20250826_161226_-14.9C_.fit",
        region: "K074-3",
        system: "GY1",
        ra: "20.30000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:12:26",
        startTime: "2025-08-26 17:00:02,600",
        endTime: "2025-08-26 17:34:11,699",
        relativeTime: 5
    },
    {
        filename: "GY1_K074-4_No Filter_60S_Bin2_UTC20250826_161420_-15C_.fit",
        region: "K074-4",
        system: "GY1",
        ra: "19.50000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:14:20",
        startTime: "2025-08-26 17:00:02,612",
        endTime: "2025-08-26 17:04:48,441",
        relativeTime: 5
    },
    {
        filename: "GY1_K074-5_No Filter_60S_Bin2_UTC20250826_161612_-15C_.fit",
        region: "K074-5",
        system: "GY1",
        ra: "19.90000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:16:12",
        startTime: "2025-08-26 17:00:02,616",
        endTime: "2025-08-26 17:04:39,110",
        relativeTime: 5
    },
    {
        filename: "GY1_K074-6_No Filter_60S_Bin2_UTC20250826_161759_-14.9C_.fit",
        region: "K074-6",
        system: "GY1",
        ra: "20.30000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:17:59",
        startTime: "2025-08-26 17:00:02,618",
        endTime: "2025-08-26 17:34:28,904",
        relativeTime: 5
    },
    {
        filename: "GY2_K074-1_No Filter_60S_Bin2_UTC20250826_160848_-14.9C_.fit",
        region: "K074-1",
        system: "GY2",
        ra: "19.70000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:08:48",
        startTime: "2025-08-26 17:00:02,998",
        endTime: "2025-08-26 17:33:59,451",
        relativeTime: 5
    },
    {
        filename: "GY2_K074-2_No Filter_60S_Bin2_UTC20250826_161037_-14.9C_.fit",
        region: "K074-2",
        system: "GY2",
        ra: "20.10000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:10:37",
        startTime: "2025-08-26 17:00:03,006",
        endTime: "2025-08-26 17:33:32,276",
        relativeTime: 5
    },
    {
        filename: "GY2_K074-3_No Filter_60S_Bin2_UTC20250826_161225_-15C_.fit",
        region: "K074-3",
        system: "GY2",
        ra: "20.50000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:12:25",
        startTime: "2025-08-26 17:00:03,012",
        endTime: "2025-08-26 17:33:19,086",
        relativeTime: 5
    },
    {
        filename: "GY2_K074-4_No Filter_60S_Bin2_UTC20250826_161421_-15C_.fit",
        region: "K074-4",
        system: "GY2",
        ra: "19.70000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:14:21",
        startTime: "2025-08-26 17:00:03,019",
        endTime: "2025-08-26 17:04:07,252",
        relativeTime: 5
    },
    {
        filename: "GY2_K074-5_No Filter_60S_Bin2_UTC20250826_161611_-15C_.fit",
        region: "K074-5",
        system: "GY2",
        ra: "20.10000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:16:11",
        startTime: "2025-08-26 17:00:03,024",
        endTime: "2025-08-26 17:03:56,571",
        relativeTime: 5
    },
    {
        filename: "GY2_K074-6_No Filter_60S_Bin2_UTC20250826_161759_-15C_.fit",
        region: "K074-6",
        system: "GY2",
        ra: "20.50000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:17:59",
        startTime: "2025-08-26 17:00:03,031",
        endTime: "2025-08-26 17:33:24,210",
        relativeTime: 5
    },
    {
        filename: "GY3_K074-1_No Filter_60S_Bin2_UTC20250826_160848_-14.9C_.fit",
        region: "K074-1",
        system: "GY3",
        ra: "19.50000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:08:48",
        startTime: "2025-08-26 17:03:41,762",
        endTime: "2025-08-26 17:07:05,318",
        relativeTime: 5
    },
    {
        filename: "GY3_K074-2_No Filter_60S_Bin2_UTC20250826_161037_-14.9C_.fit",
        region: "K074-2",
        system: "GY3",
        ra: "19.90000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:10:37",
        startTime: "2025-08-26 17:03:50,774",
        endTime: "2025-08-26 17:37:54,547",
        relativeTime: 5
    },
    {
        filename: "GY3_K074-3_No Filter_60S_Bin2_UTC20250826_161226_-15C_.fit",
        region: "K074-3",
        system: "GY3",
        ra: "20.30000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:12:26",
        startTime: "2025-08-26 17:03:56,575",
        endTime: "2025-08-26 17:37:24,027",
        relativeTime: 5
    },
    {
        filename: "GY3_K074-4_No Filter_60S_Bin2_UTC20250826_161422_-14.9C_.fit",
        region: "K074-4",
        system: "GY3",
        ra: "19.50000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:14:22",
        startTime: "2025-08-26 17:03:57,989",
        endTime: "2025-08-26 17:08:17,497",
        relativeTime: 5
    },
    {
        filename: "GY3_K074-5_No Filter_60S_Bin2_UTC20250826_161611_-15C_.fit",
        region: "K074-5",
        system: "GY3",
        ra: "19.90000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:16:11",
        startTime: "2025-08-26 17:03:59,382",
        endTime: "2025-08-26 17:07:54,339",
        relativeTime: 5
    },
    {
        filename: "GY3_K074-6_No Filter_60S_Bin2_UTC20250826_161800_-15C_.fit",
        region: "K074-6",
        system: "GY3",
        ra: "20.30000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:18:00",
        startTime: "2025-08-26 17:04:05,802",
        endTime: "2025-08-26 17:08:23,475",
        relativeTime: 5
    },
    {
        filename: "GY4_K074-1_No Filter_60S_Bin2_UTC20250826_160847_-14.9C_.fit",
        region: "K074-1",
        system: "GY4",
        ra: "19.70000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:08:47",
        startTime: "2025-08-26 17:08:59,062",
        endTime: "2025-08-26 17:14:56,232",
        relativeTime: 5
    },
    {
        filename: "GY4_K074-2_No Filter_60S_Bin2_UTC20250826_161038_-15C_.fit",
        region: "K074-2",
        system: "GY4",
        ra: "20.10000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:10:38",
        startTime: "2025-08-26 17:09:32,352",
        endTime: "2025-08-26 17:44:21,647",
        relativeTime: 5
    },
    {
        filename: "GY4_K074-3_No Filter_60S_Bin2_UTC20250826_161226_-14.9C_.fit",
        region: "K074-3",
        system: "GY4",
        ra: "20.50000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:12:26",
        startTime: "2025-08-26 17:09:45,514",
        endTime: "2025-08-26 17:44:22,786",
        relativeTime: 5
    },
    {
        filename: "GY4_K074-4_No Filter_60S_Bin2_UTC20250826_161422_-14.9C_.fit",
        region: "K074-4",
        system: "GY4",
        ra: "19.70000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:14:22",
        startTime: "2025-08-26 17:09:51,007",
        endTime: "2025-08-26 17:13:54,040",
        relativeTime: 5
    },
    {
        filename: "GY4_K074-5_No Filter_60S_Bin2_UTC20250826_161611_-15C_.fit",
        region: "K074-5",
        system: "GY4",
        ra: "20.10000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:16:11",
        startTime: "2025-08-26 17:10:33,351",
        endTime: "2025-08-26 17:46:36,421",
        relativeTime: 5
    },
    {
        filename: "GY4_K074-6_No Filter_60S_Bin2_UTC20250826_161759_-15C_.fit",
        region: "K074-6",
        system: "GY4",
        ra: "20.50000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:17:59",
        startTime: "2025-08-26 17:11:32,661",
        endTime: "2025-08-26 17:46:19,802",
        relativeTime: 5
    },
    {
        filename: "GY5_K074-1_No Filter_60S_Bin2_UTC20250826_160847_-15C_.fit",
        region: "K074-1",
        system: "GY5",
        ra: "19.50000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:08:47",
        startTime: "2025-08-26 17:30:09,912",
        endTime: "2025-08-26 18:09:31,263",
        relativeTime: 5
    },
    {
        filename: "GY5_K074-2_No Filter_60S_Bin2_UTC20250826_161038_-14.9C_.fit",
        region: "K074-2",
        system: "GY5",
        ra: "19.90000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:10:38",
        startTime: "2025-08-26 17:30:10,429",
        endTime: "2025-08-26 18:05:29,464",
        relativeTime: 5
    },
    {
        filename: "GY5_K074-3_No Filter_60S_Bin2_UTC20250826_161226_-15C_.fit",
        region: "K074-3",
        system: "GY5",
        ra: "20.30000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:12:26",
        startTime: "2025-08-26 17:30:10,440",
        endTime: "2025-08-26 17:34:41,894",
        relativeTime: 5
    },
    {
        filename: "GY5_K074-4_No Filter_60S_Bin2_UTC20250826_161420_-15C_.fit",
        region: "K074-4",
        system: "GY5",
        ra: "19.50000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:14:20",
        startTime: "2025-08-26 17:30:10,540",
        endTime: "2025-08-26 17:34:55,541",
        relativeTime: 5
    },
    {
        filename: "GY5_K074-5_No Filter_60S_Bin2_UTC20250826_161611_-15C_.fit",
        region: "K074-5",
        system: "GY5",
        ra: "19.90000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:16:11",
        startTime: "2025-08-26 17:30:10,820",
        endTime: "2025-08-26 17:35:07,675",
        relativeTime: 5
    },
    {
        filename: "GY5_K074-6_No Filter_60S_Bin2_UTC20250826_161800_-15C_.fit",
        region: "K074-6",
        system: "GY5",
        ra: "20.30000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:18:00",
        startTime: "2025-08-26 17:30:10,952",
        endTime: "2025-08-26 18:05:22,619",
        relativeTime: 5
    },
    {
        filename: "GY6_K074-1_No Filter_60S_Bin2_UTC20250826_160848_-15C_.fit",
        region: "K074-1",
        system: "GY6",
        ra: "19.70000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:08:48",
        startTime: "2025-08-26 17:34:26,282",
        endTime: "2025-08-26 18:10:41,237",
        relativeTime: 5
    },
    {
        filename: "GY6_K074-2_No Filter_60S_Bin2_UTC20250826_161037_-14.9C_.fit",
        region: "K074-2",
        system: "GY6",
        ra: "20.10000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:10:37",
        startTime: "2025-08-26 17:34:26,896",
        endTime: "2025-08-26 18:06:30,067",
        relativeTime: 5
    },
    {
        filename: "GY6_K074-3_No Filter_60S_Bin2_UTC20250826_161226_-15C_.fit",
        region: "K074-3",
        system: "GY6",
        ra: "20.50000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:12:26",
        startTime: "2025-08-26 17:34:28,924",
        endTime: "2025-08-26 18:03:11,725",
        relativeTime: 5
    },
    {
        filename: "GY6_K074-4_No Filter_60S_Bin2_UTC20250826_161421_-14.9C_.fit",
        region: "K074-4",
        system: "GY6",
        ra: "19.70000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:14:21",
        startTime: "2025-08-26 17:34:29,299",
        endTime: "2025-08-26 17:38:59,123",
        relativeTime: 5
    },
    {
        filename: "GY6_K074-5_No Filter_60S_Bin2_UTC20250826_161612_-14.9C_.fit",
        region: "K074-5",
        system: "GY6",
        ra: "20.10000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:16:12",
        startTime: "2025-08-26 17:34:34,356",
        endTime: "2025-08-26 18:10:07,764",
        relativeTime: 5
    },
    {
        filename: "GY6_K074-6_No Filter_60S_Bin2_UTC20250826_161800_-15C_.fit",
        region: "K074-6",
        system: "GY6",
        ra: "20.50000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:18:00",
        startTime: "2025-08-26 17:34:35,043",
        endTime: "2025-08-26 18:06:45,199",
        relativeTime: 5
    },
    {
        filename: "GY1_K074-1_No Filter_60S_Bin2_UTC20250826_162529_-15C_.fit",
        region: "K074-1",
        system: "GY1",
        ra: "19.50000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:25:29",
        startTime: "2025-08-26 17:00:02,628",
        endTime: "2025-08-26 17:04:16,248",
        relativeTime: 6
    },
    {
        filename: "GY1_K074-2_No Filter_60S_Bin2_UTC20250826_162720_-14.9C_.fit",
        region: "K074-2",
        system: "GY1",
        ra: "19.90000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:27:20",
        startTime: "2025-08-26 17:00:02,633",
        endTime: "2025-08-26 17:34:35,022",
        relativeTime: 6
    },
    {
        filename: "GY1_K074-7_No Filter_60S_Bin2_UTC20250826_161955_-15C_.fit",
        region: "K074-7",
        system: "GY1",
        ra: "19.50000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:19:55",
        startTime: "2025-08-26 17:00:02,622",
        endTime: "2025-08-26 17:02:56,385",
        relativeTime: 6
    },
    {
        filename: "GY1_K074-8_No Filter_60S_Bin2_UTC20250826_162145_-14.9C_.fit",
        region: "K074-8",
        system: "GY1",
        ra: "19.90000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:21:45",
        startTime: "2025-08-26 17:00:02,624",
        endTime: "2025-08-26 17:03:40,190",
        relativeTime: 6
    },
    {
        filename: "GY1_K074-9_No Filter_60S_Bin2_UTC20250826_162336_-14.9C_.fit",
        region: "K074-9",
        system: "GY1",
        ra: "20.30000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:23:36",
        startTime: "2025-08-26 17:00:02,649",
        endTime: "2025-08-26 17:34:46,053",
        relativeTime: 6
    },
    {
        filename: "GY2_K074-1_No Filter_60S_Bin2_UTC20250826_162530_-15C_.fit",
        region: "K074-1",
        system: "GY2",
        ra: "19.70000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:25:30",
        startTime: "2025-08-26 17:00:03,044",
        endTime: "2025-08-26 17:34:04,632",
        relativeTime: 6
    },
    {
        filename: "GY2_K074-2_No Filter_60S_Bin2_UTC20250826_162718_-14.9C_.fit",
        region: "K074-2",
        system: "GY2",
        ra: "20.10000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:27:18",
        startTime: "2025-08-26 17:00:03,050",
        endTime: "2025-08-26 17:33:40,756",
        relativeTime: 6
    },
    {
        filename: "GY2_K074-7_No Filter_60S_Bin2_UTC20250826_161955_-15C_.fit",
        region: "K074-7",
        system: "GY2",
        ra: "19.70000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:19:55",
        startTime: "2025-08-26 17:00:03,038",
        endTime: "2025-08-26 17:03:44,772",
        relativeTime: 6
    },
    {
        filename: "GY2_K074-8_No Filter_60S_Bin2_UTC20250826_162145_-15C_.fit",
        region: "K074-8",
        system: "GY2",
        ra: "20.10000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:21:45",
        startTime: "2025-08-26 17:00:03,105",
        endTime: "2025-08-26 17:34:19,151",
        relativeTime: 6
    },
    {
        filename: "GY2_K074-9_No Filter_60S_Bin2_UTC20250826_162336_-14.9C_.fit",
        region: "K074-9",
        system: "GY2",
        ra: "20.50000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:23:36",
        startTime: "2025-08-26 17:00:03,728",
        endTime: "2025-08-26 17:03:04,313",
        relativeTime: 6
    },
    {
        filename: "GY3_K074-1_No Filter_60S_Bin2_UTC20250826_162528_-15C_.fit",
        region: "K074-1",
        system: "GY3",
        ra: "19.50000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:25:28",
        startTime: "2025-08-26 17:03:56,262",
        endTime: "2025-08-26 17:07:39,562",
        relativeTime: 6
    },
    {
        filename: "GY3_K074-2_No Filter_60S_Bin2_UTC20250826_162719_-14.9C_.fit",
        region: "K074-2",
        system: "GY3",
        ra: "19.90000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:27:19",
        startTime: "2025-08-26 17:04:14,895",
        endTime: "2025-08-26 17:38:24,987",
        relativeTime: 6
    },
    {
        filename: "GY3_K074-7_No Filter_60S_Bin2_UTC20250826_161955_-15C_.fit",
        region: "K074-7",
        system: "GY3",
        ra: "19.50000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:19:55",
        startTime: "2025-08-26 17:04:06,348",
        endTime: "2025-08-26 17:07:57,625",
        relativeTime: 6
    },
    {
        filename: "GY3_K074-8_No Filter_60S_Bin2_UTC20250826_162145_-15C_.fit",
        region: "K074-8",
        system: "GY3",
        ra: "19.90000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:21:45",
        startTime: "2025-08-26 17:04:07,256",
        endTime: "2025-08-26 17:08:17,782",
        relativeTime: 6
    },
    {
        filename: "GY3_K074-9_No Filter_60S_Bin2_UTC20250826_162335_-15C_.fit",
        region: "K074-9",
        system: "GY3",
        ra: "20.30000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:23:35",
        startTime: "2025-08-26 17:04:07,814",
        endTime: "2025-08-26 17:07:43,008",
        relativeTime: 6
    },
    {
        filename: "GY4_K074-1_No Filter_60S_Bin2_UTC20250826_162529_-14.9C_.fit",
        region: "K074-1",
        system: "GY4",
        ra: "19.70000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:25:29",
        startTime: "2025-08-26 17:10:11,380",
        endTime: "2025-08-26 17:16:17,791",
        relativeTime: 6
    },
    {
        filename: "GY4_K074-2_No Filter_60S_Bin2_UTC20250826_162718_-14.9C_.fit",
        region: "K074-2",
        system: "GY4",
        ra: "20.10000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:27:18",
        startTime: "2025-08-26 17:10:18,451",
        endTime: "2025-08-26 17:45:06,670",
        relativeTime: 6
    },
    {
        filename: "GY4_K074-7_No Filter_60S_Bin2_UTC20250826_161955_-15C_.fit",
        region: "K074-7",
        system: "GY4",
        ra: "19.70000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:19:55",
        startTime: "2025-08-26 17:12:24,473",
        endTime: "2025-08-26 17:17:39,922",
        relativeTime: 6
    },
    {
        filename: "GY4_K074-8_No Filter_60S_Bin2_UTC20250826_162146_-14.9C_.fit",
        region: "K074-8",
        system: "GY4",
        ra: "20.10000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:21:46",
        startTime: "2025-08-26 17:12:51,033",
        endTime: "2025-08-26 17:17:02,539",
        relativeTime: 6
    },
    {
        filename: "GY4_K074-9_No Filter_60S_Bin2_UTC20250826_162335_-15C_.fit",
        region: "K074-9",
        system: "GY4",
        ra: "20.50000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:23:35",
        startTime: "2025-08-26 17:13:03,645",
        endTime: "2025-08-26 17:17:55,203",
        relativeTime: 6
    },
    {
        filename: "GY5_K074-1_No Filter_60S_Bin2_UTC20250826_162529_-15C_.fit",
        region: "K074-1",
        system: "GY5",
        ra: "19.50000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:25:29",
        startTime: "2025-08-26 17:30:10,954",
        endTime: "2025-08-26 17:35:08,513",
        relativeTime: 6
    },
    {
        filename: "GY5_K074-2_No Filter_60S_Bin2_UTC20250826_162718_-14.9C_.fit",
        region: "K074-2",
        system: "GY5",
        ra: "19.90000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:27:18",
        startTime: "2025-08-26 17:30:38,998",
        endTime: "2025-08-26 18:05:53,094",
        relativeTime: 6
    },
    {
        filename: "GY5_K074-7_No Filter_60S_Bin2_UTC20250826_161954_-15C_.fit",
        region: "K074-7",
        system: "GY5",
        ra: "19.50000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:19:54",
        startTime: "2025-08-26 17:33:24,220",
        endTime: "2025-08-26 17:37:55,247",
        relativeTime: 6
    },
    {
        filename: "GY5_K074-8_No Filter_60S_Bin2_UTC20250826_162145_-15C_.fit",
        region: "K074-8",
        system: "GY5",
        ra: "19.90000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:21:45",
        startTime: "2025-08-26 17:33:24,590",
        endTime: "2025-08-26 17:38:16,917",
        relativeTime: 6
    },
    {
        filename: "GY5_K074-9_No Filter_60S_Bin2_UTC20250826_162335_-14.9C_.fit",
        region: "K074-9",
        system: "GY5",
        ra: "20.30000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:23:35",
        startTime: "2025-08-26 17:33:27,092",
        endTime: "2025-08-26 17:37:08,728",
        relativeTime: 6
    },
    {
        filename: "GY6_K074-1_No Filter_60S_Bin2_UTC20250826_162530_-14.9C_.fit",
        region: "K074-1",
        system: "GY6",
        ra: "19.70000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:25:30",
        startTime: "2025-08-26 17:34:41,377",
        endTime: "2025-08-26 17:38:45,017",
        relativeTime: 6
    },
    {
        filename: "GY6_K074-2_No Filter_60S_Bin2_UTC20250826_162718_-14.9C_.fit",
        region: "K074-2",
        system: "GY6",
        ra: "20.10000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:27:18",
        startTime: "2025-08-26 17:34:41,899",
        endTime: "2025-08-26 18:09:12,530",
        relativeTime: 6
    },
    {
        filename: "GY6_K074-7_No Filter_60S_Bin2_UTC20250826_161954_-14.9C_.fit",
        region: "K074-7",
        system: "GY6",
        ra: "19.70000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:19:54",
        startTime: "2025-08-26 17:34:35,711",
        endTime: "2025-08-26 17:39:13,630",
        relativeTime: 6
    },
    {
        filename: "GY6_K074-8_No Filter_60S_Bin2_UTC20250826_162145_-15C_.fit",
        region: "K074-8",
        system: "GY6",
        ra: "20.10000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:21:45",
        startTime: "2025-08-26 17:34:57,689",
        endTime: "2025-08-26 17:39:09,451",
        relativeTime: 6
    },
    {
        filename: "GY6_K074-9_No Filter_60S_Bin2_UTC20250826_162336_-15C_.fit",
        region: "K074-9",
        system: "GY6",
        ra: "20.50000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:23:36",
        startTime: "2025-08-26 17:35:06,973",
        endTime: "2025-08-26 18:09:08,240",
        relativeTime: 6
    },
    {
        filename: "GY1_K074-3_No Filter_60S_Bin2_UTC20250826_162907_-14.9C_.fit",
        region: "K074-3",
        system: "GY1",
        ra: "20.30000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:29:07",
        startTime: "2025-08-26 17:00:02,634",
        endTime: "2025-08-26 17:34:05,609",
        relativeTime: 7
    },
    {
        filename: "GY1_K074-4_No Filter_60S_Bin2_UTC20250826_163102_-15C_.fit",
        region: "K074-4",
        system: "GY1",
        ra: "19.50000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:31:02",
        startTime: "2025-08-26 17:00:02,638",
        endTime: "2025-08-26 17:04:30,844",
        relativeTime: 7
    },
    {
        filename: "GY1_K074-5_No Filter_60S_Bin2_UTC20250826_163252_-14.9C_.fit",
        region: "K074-5",
        system: "GY1",
        ra: "19.90000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:32:52",
        startTime: "2025-08-26 17:00:02,638",
        endTime: "2025-08-26 17:35:19,243",
        relativeTime: 7
    },
    {
        filename: "GY1_K074-6_No Filter_60S_Bin2_UTC20250826_163441_-14.9C_.fit",
        region: "K074-6",
        system: "GY1",
        ra: "20.30000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:34:41",
        startTime: "2025-08-26 17:00:02,647",
        endTime: "2025-08-26 17:34:24,874",
        relativeTime: 7
    },
    {
        filename: "GY1_K074-7_No Filter_60S_Bin2_UTC20250826_163636_-15C_.fit",
        region: "K074-7",
        system: "GY1",
        ra: "19.50000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:36:36",
        startTime: "2025-08-26 17:00:02,648",
        endTime: "2025-08-26 17:03:20,108",
        relativeTime: 7
    },
    {
        filename: "GY1_K074-8_No Filter_60S_Bin2_UTC20250826_163826_-15C_.fit",
        region: "K074-8",
        system: "GY1",
        ra: "19.90000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:38:26",
        startTime: "2025-08-26 17:00:02,673",
        endTime: "2025-08-26 17:04:29,256",
        relativeTime: 7
    },
    {
        filename: "GY2_K074-3_No Filter_60S_Bin2_UTC20250826_162907_-14.9C_.fit",
        region: "K074-3",
        system: "GY2",
        ra: "20.50000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:29:07",
        startTime: "2025-08-26 17:00:03,055",
        endTime: "2025-08-26 17:33:24,582",
        relativeTime: 7
    },
    {
        filename: "GY2_K074-4_No Filter_60S_Bin2_UTC20250826_163103_-15C_.fit",
        region: "K074-4",
        system: "GY2",
        ra: "19.70000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:31:03",
        startTime: "2025-08-26 17:00:03,064",
        endTime: "2025-08-26 17:03:21,620",
        relativeTime: 7
    },
    {
        filename: "GY2_K074-5_No Filter_60S_Bin2_UTC20250826_163252_-14.9C_.fit",
        region: "K074-5",
        system: "GY2",
        ra: "20.10000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:32:52",
        startTime: "2025-08-26 17:00:03,069",
        endTime: "2025-08-26 17:03:57,984",
        relativeTime: 7
    },
    {
        filename: "GY2_K074-6_No Filter_60S_Bin2_UTC20250826_163442_-15C_.fit",
        region: "K074-6",
        system: "GY2",
        ra: "20.50000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:34:42",
        startTime: "2025-08-26 17:00:03,077",
        endTime: "2025-08-26 17:33:32,180",
        relativeTime: 7
    },
    {
        filename: "GY2_K074-7_No Filter_60S_Bin2_UTC20250826_163636_-15C_.fit",
        region: "K074-7",
        system: "GY2",
        ra: "19.70000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:36:36",
        startTime: "2025-08-26 17:00:08,245",
        endTime: "2025-08-26 17:02:47,223",
        relativeTime: 7
    },
    {
        filename: "GY2_K074-8_No Filter_60S_Bin2_UTC20250826_163825_-15C_.fit",
        region: "K074-8",
        system: "GY2",
        ra: "20.10000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:38:25",
        startTime: "2025-08-26 17:00:08,257",
        endTime: "2025-08-26 17:04:07,812",
        relativeTime: 7
    },
    {
        filename: "GY3_K074-3_No Filter_60S_Bin2_UTC20250826_162908_-14.9C_.fit",
        region: "K074-3",
        system: "GY3",
        ra: "20.30000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:29:08",
        startTime: "2025-08-26 17:04:16,020",
        endTime: "2025-08-26 17:38:34,389",
        relativeTime: 7
    },
    {
        filename: "GY3_K074-4_No Filter_60S_Bin2_UTC20250826_163103_-15C_.fit",
        region: "K074-4",
        system: "GY3",
        ra: "19.50000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:31:03",
        startTime: "2025-08-26 17:04:16,253",
        endTime: "2025-08-26 17:08:49,754",
        relativeTime: 7
    },
    {
        filename: "GY3_K074-5_No Filter_60S_Bin2_UTC20250826_163252_-15C_.fit",
        region: "K074-5",
        system: "GY3",
        ra: "19.90000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:32:52",
        startTime: "2025-08-26 17:04:18,800",
        endTime: "2025-08-26 17:07:56,357",
        relativeTime: 7
    },
    {
        filename: "GY3_K074-6_No Filter_60S_Bin2_UTC20250826_163442_-15C_.fit",
        region: "K074-6",
        system: "GY3",
        ra: "20.30000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:34:42",
        startTime: "2025-08-26 17:04:29,260",
        endTime: "2025-08-26 17:08:48,111",
        relativeTime: 7
    },
    {
        filename: "GY3_K074-7_No Filter_60S_Bin2_UTC20250826_163636_-14.9C_.fit",
        region: "K074-7",
        system: "GY3",
        ra: "19.50000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:36:36",
        startTime: "2025-08-26 17:04:30,036",
        endTime: "2025-08-26 17:08:44,591",
        relativeTime: 7
    },
    {
        filename: "GY3_K074-8_No Filter_60S_Bin2_UTC20250826_163825_-15C_.fit",
        region: "K074-8",
        system: "GY3",
        ra: "19.90000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:38:25",
        startTime: "2025-08-26 17:04:30,849",
        endTime: "2025-08-26 17:08:44,147",
        relativeTime: 7
    },
    {
        filename: "GY4_K074-3_No Filter_60S_Bin2_UTC20250826_162907_-14.9C_.fit",
        region: "K074-3",
        system: "GY4",
        ra: "20.50000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:29:07",
        startTime: "2025-08-26 17:10:18,809",
        endTime: "2025-08-26 17:38:27,017",
        relativeTime: 7
    },
    {
        filename: "GY4_K074-4_No Filter_60S_Bin2_UTC20250826_163102_-15C_.fit",
        region: "K074-4",
        system: "GY4",
        ra: "19.70000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:31:02",
        startTime: "2025-08-26 17:14:18,158",
        endTime: "2025-08-26 17:18:51,021",
        relativeTime: 7
    },
    {
        filename: "GY4_K074-5_No Filter_60S_Bin2_UTC20250826_163251_-14.9C_.fit",
        region: "K074-5",
        system: "GY4",
        ra: "20.10000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:32:51",
        startTime: "2025-08-26 17:14:26,707",
        endTime: "2025-08-26 17:51:03,234",
        relativeTime: 7
    },
    {
        filename: "GY4_K074-6_No Filter_60S_Bin2_UTC20250826_163442_-15C_.fit",
        region: "K074-6",
        system: "GY4",
        ra: "20.50000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:34:42",
        startTime: "2025-08-26 17:14:27,676",
        endTime: "2025-08-26 17:49:53,047",
        relativeTime: 7
    },
    {
        filename: "GY4_K074-7_No Filter_60S_Bin2_UTC20250826_163637_-14.9C_.fit",
        region: "K074-7",
        system: "GY4",
        ra: "19.70000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:36:37",
        startTime: "2025-08-26 17:14:56,239",
        endTime: "2025-08-26 17:19:54,501",
        relativeTime: 7
    },
    {
        filename: "GY4_K074-8_No Filter_60S_Bin2_UTC20250826_163825_-15C_.fit",
        region: "K074-8",
        system: "GY4",
        ra: "20.10000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:38:25",
        startTime: "2025-08-26 17:16:17,834",
        endTime: "2025-08-26 17:20:30,047",
        relativeTime: 7
    },
    {
        filename: "GY5_K074-3_No Filter_60S_Bin2_UTC20250826_162909_-14.9C_.fit",
        region: "K074-3",
        system: "GY5",
        ra: "20.30000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:29:09",
        startTime: "2025-08-26 17:30:51,696",
        endTime: "2025-08-26 17:35:23,117",
        relativeTime: 7
    },
    {
        filename: "GY5_K074-4_No Filter_60S_Bin2_UTC20250826_163103_-15C_.fit",
        region: "K074-4",
        system: "GY5",
        ra: "19.50000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:31:03",
        startTime: "2025-08-26 17:32:26,360",
        endTime: "2025-08-26 17:36:15,764",
        relativeTime: 7
    },
    {
        filename: "GY5_K074-5_No Filter_60S_Bin2_UTC20250826_163252_-14.9C_.fit",
        region: "K074-5",
        system: "GY5",
        ra: "19.90000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:32:52",
        startTime: "2025-08-26 17:33:19,096",
        endTime: "2025-08-26 17:38:22,327",
        relativeTime: 7
    },
    {
        filename: "GY5_K074-6_No Filter_60S_Bin2_UTC20250826_163441_-15C_.fit",
        region: "K074-6",
        system: "GY5",
        ra: "20.30000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:34:41",
        startTime: "2025-08-26 17:33:40,766",
        endTime: "2025-08-26 18:07:57,849",
        relativeTime: 7
    },
    {
        filename: "GY5_K074-7_No Filter_60S_Bin2_UTC20250826_163637_-15C_.fit",
        region: "K074-7",
        system: "GY5",
        ra: "19.50000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:36:37",
        startTime: "2025-08-26 17:33:45,743",
        endTime: "2025-08-26 17:38:00,862",
        relativeTime: 7
    },
    {
        filename: "GY5_K074-8_No Filter_60S_Bin2_UTC20250826_163826_-15C_.fit",
        region: "K074-8",
        system: "GY5",
        ra: "19.90000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:38:26",
        startTime: "2025-08-26 17:33:47,370",
        endTime: "2025-08-26 17:38:29,090",
        relativeTime: 7
    },
    {
        filename: "GY6_K074-3_No Filter_60S_Bin2_UTC20250826_162909_-14.9C_.fit",
        region: "K074-3",
        system: "GY6",
        ra: "20.50000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:29:09",
        startTime: "2025-08-26 17:34:45,090",
        endTime: "2025-08-26 18:08:11,861",
        relativeTime: 7
    },
    {
        filename: "GY6_K074-4_No Filter_60S_Bin2_UTC20250826_163103_-15C_.fit",
        region: "K074-4",
        system: "GY6",
        ra: "19.70000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:31:03",
        startTime: "2025-08-26 17:34:46,070",
        endTime: "2025-08-26 17:39:15,058",
        relativeTime: 7
    },
    {
        filename: "GY6_K074-5_No Filter_60S_Bin2_UTC20250826_163251_-15C_.fit",
        region: "K074-5",
        system: "GY6",
        ra: "20.10000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:32:51",
        startTime: "2025-08-26 17:34:52,252",
        endTime: "2025-08-26 18:09:45,970",
        relativeTime: 7
    },
    {
        filename: "GY6_K074-6_No Filter_60S_Bin2_UTC20250826_163442_-15C_.fit",
        region: "K074-6",
        system: "GY6",
        ra: "20.50000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:34:42",
        startTime: "2025-08-26 17:34:55,544",
        endTime: "2025-08-26 18:06:47,859",
        relativeTime: 7
    },
    {
        filename: "GY6_K074-7_No Filter_60S_Bin2_UTC20250826_163636_-14.9C_.fit",
        region: "K074-7",
        system: "GY6",
        ra: "19.70000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:36:36",
        startTime: "2025-08-26 17:36:15,767",
        endTime: "2025-08-26 17:39:39,033",
        relativeTime: 7
    },
    {
        filename: "GY6_K074-8_No Filter_60S_Bin2_UTC20250826_163826_-15C_.fit",
        region: "K074-8",
        system: "GY6",
        ra: "20.10000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:38:26",
        startTime: "2025-08-26 17:36:37,897",
        endTime: "2025-08-26 17:40:27,344",
        relativeTime: 7
    },
    {
        filename: "GY1_K074-1_No Filter_60S_Bin2_UTC20250826_164210_-15C_.fit",
        region: "K074-1",
        system: "GY1",
        ra: "19.50000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:42:10",
        startTime: "2025-08-26 17:00:02,652",
        endTime: "2025-08-26 17:34:52,239",
        relativeTime: 8
    },
    {
        filename: "GY1_K074-2_No Filter_60S_Bin2_UTC20250826_164400_-15C_.fit",
        region: "K074-2",
        system: "GY1",
        ra: "19.90000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:44:00",
        startTime: "2025-08-26 17:00:02,656",
        endTime: "2025-08-26 17:34:45,034",
        relativeTime: 8
    },
    {
        filename: "GY1_K074-3_No Filter_60S_Bin2_UTC20250826_164550_-15C_.fit",
        region: "K074-3",
        system: "GY1",
        ra: "20.30000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:45:50",
        startTime: "2025-08-26 17:00:02,660",
        endTime: "2025-08-26 17:34:14,962",
        relativeTime: 8
    },
    {
        filename: "GY1_K074-4_No Filter_60S_Bin2_UTC20250826_164743_-14.9C_.fit",
        region: "K074-4",
        system: "GY1",
        ra: "19.50000h",
        dec: "+3.98",
        utcTime: "2025-08-26 16:47:43",
        startTime: "2025-08-26 17:00:02,663",
        endTime: "2025-08-26 17:34:01,551",
        relativeTime: 8
    },
    {
        filename: "GY1_K074-9_No Filter_60S_Bin2_UTC20250826_164016_-15C_.fit",
        region: "K074-9",
        system: "GY1",
        ra: "20.30000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:40:16",
        startTime: "2025-08-26 17:00:02,677",
        endTime: "2025-08-26 17:33:52,622",
        relativeTime: 8
    },
    {
        filename: "GY2_K074-1_No Filter_60S_Bin2_UTC20250826_164211_-14.9C_.fit",
        region: "K074-1",
        system: "GY2",
        ra: "19.70000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:42:11",
        startTime: "2025-08-26 17:00:03,769",
        endTime: "2025-08-26 17:41:00,102",
        relativeTime: 8
    },
    {
        filename: "GY2_K074-2_No Filter_60S_Bin2_UTC20250826_164359_-14.9C_.fit",
        region: "K074-2",
        system: "GY2",
        ra: "20.10000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:43:59",
        startTime: "2025-08-26 17:00:08,177",
        endTime: "2025-08-26 17:32:26,346",
        relativeTime: 8
    },
    {
        filename: "GY2_K074-3_No Filter_60S_Bin2_UTC20250826_164550_-15C_.fit",
        region: "K074-3",
        system: "GY2",
        ra: "20.50000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:45:50",
        startTime: "2025-08-26 17:00:08,192",
        endTime: "2025-08-26 17:26:17,024",
        relativeTime: 8
    },
    {
        filename: "GY2_K074-4_No Filter_60S_Bin2_UTC20250826_164743_-15C_.fit",
        region: "K074-4",
        system: "GY2",
        ra: "19.70000h",
        dec: "+3.98",
        utcTime: "2025-08-26 16:47:43",
        startTime: "2025-08-26 17:00:08,239",
        endTime: "2025-08-26 17:38:14,819",
        relativeTime: 8
    },
    {
        filename: "GY2_K074-9_No Filter_60S_Bin2_UTC20250826_164015_-15C_.fit",
        region: "K074-9",
        system: "GY2",
        ra: "20.50000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:40:15",
        startTime: "2025-08-26 17:00:08,258",
        endTime: "2025-08-26 17:03:02,671",
        relativeTime: 8
    },
    {
        filename: "GY3_K074-1_No Filter_60S_Bin2_UTC20250826_164209_-14.9C_.fit",
        region: "K074-1",
        system: "GY3",
        ra: "19.50000h",
        dec: "-3.02",
        utcTime: "2025-08-26 16:42:09",
        startTime: "2025-08-26 17:04:34,115",
        endTime: "2025-08-26 17:10:33,347",
        relativeTime: 8
    },
    {
        filename: "GY3_K074-2_No Filter_60S_Bin2_UTC20250826_164400_-15C_.fit",
        region: "K074-2",
        system: "GY3",
        ra: "19.90000h",
        dec: "-3.02",
        utcTime: "2025-08-26 16:44:00",
        startTime: "2025-08-26 17:04:34,153",
        endTime: "2025-08-26 17:38:45,214",
        relativeTime: 8
    },
    {
        filename: "GY3_K074-3_No Filter_60S_Bin2_UTC20250826_164549_-14.9C_.fit",
        region: "K074-3",
        system: "GY3",
        ra: "20.30000h",
        dec: "-3.02",
        utcTime: "2025-08-26 16:45:49",
        startTime: "2025-08-26 17:04:39,115",
        endTime: "2025-08-26 17:38:37,194",
        relativeTime: 8
    },
    {
        filename: "GY3_K074-4_No Filter_60S_Bin2_UTC20250826_164743_-15C_.fit",
        region: "K074-4",
        system: "GY3",
        ra: "19.50000h",
        dec: "+2.98",
        utcTime: "2025-08-26 16:47:43",
        startTime: "2025-08-26 17:04:47,629",
        endTime: "2025-08-26 17:41:01,118",
        relativeTime: 8
    },
    {
        filename: "GY3_K074-9_No Filter_60S_Bin2_UTC20250826_164016_-15C_.fit",
        region: "K074-9",
        system: "GY3",
        ra: "20.30000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:40:16",
        startTime: "2025-08-26 17:04:32,703",
        endTime: "2025-08-26 17:10:11,373",
        relativeTime: 8
    },
    {
        filename: "GY4_K074-1_No Filter_60S_Bin2_UTC20250826_164210_-15C_.fit",
        region: "K074-1",
        system: "GY4",
        ra: "19.70000h",
        dec: "-3.02",
        utcTime: "2025-08-26 16:42:10",
        startTime: "2025-08-26 17:13:13,992",
        endTime: "2025-08-26 17:19:56,612",
        relativeTime: 8
    },
    {
        filename: "GY4_K074-2_No Filter_60S_Bin2_UTC20250826_164359_-14.9C_.fit",
        region: "K074-2",
        system: "GY4",
        ra: "20.10000h",
        dec: "-3.02",
        utcTime: "2025-08-26 16:43:59",
        startTime: "2025-08-26 17:13:54,043",
        endTime: "2025-08-26 17:43:01,053",
        relativeTime: 8
    },
    {
        filename: "GY4_K074-9_No Filter_60S_Bin2_UTC20250826_164014_-15C_.fit",
        region: "K074-9",
        system: "GY4",
        ra: "20.50000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:40:14",
        startTime: "2025-08-26 17:17:02,544",
        endTime: "2025-08-26 17:22:56,259",
        relativeTime: 8
    },
    {
        filename: "GY5_K074-1_No Filter_60S_Bin2_UTC20250826_164211_-15C_.fit",
        region: "K074-1",
        system: "GY5",
        ra: "19.50000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:42:11",
        startTime: "2025-08-26 17:33:30,233",
        endTime: "2025-08-26 17:38:04,369",
        relativeTime: 8
    },
    {
        filename: "GY5_K074-2_No Filter_60S_Bin2_UTC20250826_164359_-14.9C_.fit",
        region: "K074-2",
        system: "GY5",
        ra: "19.90000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:43:59",
        startTime: "2025-08-26 17:33:32,188",
        endTime: "2025-08-26 18:09:56,591",
        relativeTime: 8
    },
    {
        filename: "GY5_K074-3_No Filter_60S_Bin2_UTC20250826_164550_-14.9C_.fit",
        region: "K074-3",
        system: "GY5",
        ra: "20.30000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:45:50",
        startTime: "2025-08-26 17:33:32,285",
        endTime: "2025-08-26 17:37:39,257",
        relativeTime: 8
    },
    {
        filename: "GY5_K074-4_No Filter_60S_Bin2_UTC20250826_164744_-14.9C_.fit",
        region: "K074-4",
        system: "GY5",
        ra: "19.50000h",
        dec: "+1.98",
        utcTime: "2025-08-26 16:47:44",
        startTime: "2025-08-26 17:33:32,535",
        endTime: "2025-08-26 18:09:48,647",
        relativeTime: 8
    },
    {
        filename: "GY5_K074-9_No Filter_60S_Bin2_UTC20250826_164015_-15C_.fit",
        region: "K074-9",
        system: "GY5",
        ra: "20.30000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:40:15",
        startTime: "2025-08-26 17:33:53,419",
        endTime: "2025-08-26 18:10:51,531",
        relativeTime: 8
    },
    {
        filename: "GY6_K074-1_No Filter_60S_Bin2_UTC20250826_164211_-15C_.fit",
        region: "K074-1",
        system: "GY6",
        ra: "19.70000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:42:11",
        startTime: "2025-08-26 17:35:07,680",
        endTime: "2025-08-26 18:10:02,807",
        relativeTime: 8
    },
    {
        filename: "GY6_K074-2_No Filter_60S_Bin2_UTC20250826_164359_-15C_.fit",
        region: "K074-2",
        system: "GY6",
        ra: "20.10000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:43:59",
        startTime: "2025-08-26 17:35:08,516",
        endTime: "2025-08-26 18:09:26,747",
        relativeTime: 8
    },
    {
        filename: "GY6_K074-3_No Filter_60S_Bin2_UTC20250826_164550_-14.9C_.fit",
        region: "K074-3",
        system: "GY6",
        ra: "20.50000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:45:50",
        startTime: "2025-08-26 17:35:19,266",
        endTime: "2025-08-26 18:05:00,247",
        relativeTime: 8
    },
    {
        filename: "GY6_K074-4_No Filter_60S_Bin2_UTC20250826_164744_-14.9C_.fit",
        region: "K074-4",
        system: "GY6",
        ra: "19.70000h",
        dec: "+1.98",
        utcTime: "2025-08-26 16:47:44",
        startTime: "2025-08-26 17:35:21,164",
        endTime: "2025-08-26 18:10:09,470",
        relativeTime: 8
    },
    {
        filename: "GY6_K074-9_No Filter_60S_Bin2_UTC20250826_164015_-15C_.fit",
        region: "K074-9",
        system: "GY6",
        ra: "20.50000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:40:15",
        startTime: "2025-08-26 17:36:44,403",
        endTime: "2025-08-26 18:11:05,403",
        relativeTime: 8
    },
    {
        filename: "GY1_K074-5_No Filter_60S_Bin2_UTC20250826_164932_-15C_.fit",
        region: "K074-5",
        system: "GY1",
        ra: "19.90000h",
        dec: "+3.98",
        utcTime: "2025-08-26 16:49:32",
        startTime: "2025-08-26 17:00:02,666",
        endTime: "2025-08-26 17:34:41,349",
        relativeTime: 9
    },
    {
        filename: "GY1_K074-6_No Filter_60S_Bin2_UTC20250826_165121_-14.9C_.fit",
        region: "K074-6",
        system: "GY1",
        ra: "20.30000h",
        dec: "+3.98",
        utcTime: "2025-08-26 16:51:21",
        startTime: "2025-08-26 17:00:02,669",
        endTime: "2025-08-26 17:33:45,717",
        relativeTime: 9
    },
    {
        filename: "GY2_K074-5_No Filter_60S_Bin2_UTC20250826_164932_-15C_.fit",
        region: "K074-5",
        system: "GY2",
        ra: "20.10000h",
        dec: "+3.98",
        utcTime: "2025-08-26 16:49:32",
        startTime: "2025-08-26 17:00:08,240",
        endTime: "2025-08-26 17:02:59,961",
        relativeTime: 9
    },
    {
        filename: "GY3_K074-5_No Filter_60S_Bin2_UTC20250826_164932_-15C_.fit",
        region: "K074-5",
        system: "GY3",
        ra: "19.90000h",
        dec: "+2.98",
        utcTime: "2025-08-26 16:49:32",
        startTime: "2025-08-26 17:04:48,447",
        endTime: "2025-08-26 17:39:28,682",
        relativeTime: 9
    },
    {
        filename: "GY3_K074-6_No Filter_60S_Bin2_UTC20250826_165122_-14.9C_.fit",
        region: "K074-6",
        system: "GY3",
        ra: "20.30000h",
        dec: "+2.98",
        utcTime: "2025-08-26 16:51:22",
        startTime: "2025-08-26 17:04:48,499",
        endTime: "2025-08-26 17:08:59,057",
        relativeTime: 9
    },
    {
        filename: "GY3_K074-7_No Filter_60S_Bin2_UTC20250826_165316_-14.9C_.fit",
        region: "K074-7",
        system: "GY3",
        ra: "19.50000h",
        dec: "+8.98",
        utcTime: "2025-08-26 16:53:16",
        startTime: "2025-08-26 17:03:40,193",
        endTime: "2025-08-26 17:06:14,325",
        relativeTime: 9
    },
    {
        filename: "GY3_K074-8_No Filter_60S_Bin2_UTC20250826_165506_-15C_.fit",
        region: "K074-8",
        system: "GY3",
        ra: "19.90000h",
        dec: "+8.98",
        utcTime: "2025-08-26 16:55:06",
        startTime: "2025-08-26 17:03:44,777",
        endTime: "2025-08-26 17:07:47,708",
        relativeTime: 9
    },
    {
        filename: "GY3_K074-9_No Filter_60S_Bin2_UTC20250826_165657_-14.9C_.fit",
        region: "K074-9",
        system: "GY3",
        ra: "20.30000h",
        dec: "+8.98",
        utcTime: "2025-08-26 16:56:57",
        startTime: "2025-08-26 17:03:55,721",
        endTime: "2025-08-26 17:09:32,347",
        relativeTime: 9
    },
    {
        filename: "GY6_K074-5_No Filter_60S_Bin2_UTC20250826_164933_-14.9C_.fit",
        region: "K074-5",
        system: "GY6",
        ra: "20.10000h",
        dec: "+1.98",
        utcTime: "2025-08-26 16:49:33",
        startTime: "2025-08-26 17:35:23,120",
        endTime: "2025-08-26 18:09:08,887",
        relativeTime: 9
    }
];

core.output("开始显示FIT文件UTC时间信息");
core.output("显示模式：绿色标签按时间顺序出现");
core.output("文件数量：301个");
core.output("时间尺度：1秒 = 10分钟实际时间");
core.output("总时长：9秒");

// 存储已显示的标签
var displayedLabels = {};

// 主时间循环
for (var currentTime = 0; currentTime < 9; currentTime++) {
    // 计算时间和进度信息
    var hours = Math.floor(currentTime / 6);  // 6秒 = 1小时 (因为1秒=10分钟)
    var minutes = Math.floor((currentTime % 6) * 10);  // 转换为分钟
    var timeDisplay = "观测时间: " + hours + ":" + String(minutes).padStart(2, '0');

    var remainingTime = 9 - currentTime;
    var remainingHours = Math.floor(remainingTime / 6);
    var remainingMinutes = Math.floor((remainingTime % 6) * 10);
    var countdownText = "倒计时: " + remainingHours + ":" + String(remainingMinutes).padStart(2, '0');

    var overallProgress = Math.floor((currentTime / 9) * 100);
    var progressText = "总体进度: " + overallProgress + "% (" + currentTime + "/" + 9 + "秒)";

    var activeCount = 0;
    var newLabelsThisSecond = 0;

    // 检查是否有新的标签需要显示
    for (var i = 0; i < fitData.length; i++) {
        var entry = fitData[i];
        var labelName = entry.system + "_" + entry.region + "_" + i;

        // 如果到了显示时间且还未显示
        if (currentTime >= entry.relativeTime && !displayedLabels[labelName]) {
            var labelText = entry.system + "_" + entry.region + " [" + entry.utcTime + "]";

            // 创建绿色标签
            LabelMgr.labelEquatorial(
                labelName,
                entry.ra,
                entry.dec,
                true,
                12,
                greenColor,
                labelText
            );

            displayedLabels[labelName] = true;
            newLabelsThisSecond++;
        }

        // 统计已显示的标签
        if (displayedLabels[labelName]) {
            activeCount++;
        }
    }

    // 显示标签出现进度（移到这里，在activeCount计算之后）
    // 计算标签进度和统计信息
    var labelProgress = Math.floor((activeCount / fitData.length) * 100);
    var labelProgressText = "标签进度: " + labelProgress + "% (" + activeCount + "/" + fitData.length + "个)";
    var currentStatsText = "UTC时间显示 - 已显示:" + activeCount + "/" + fitData.length + " 全部绿色";

    // 输出信息到控制台
    if (newLabelsThisSecond > 0) {
        // 有新标签出现时输出详细信息
        core.output("=== UTC时间显示 (第" + currentTime + "秒) ===");
        core.output(timeDisplay);
        core.output(countdownText);
        core.output(progressText);
        core.output(labelProgressText);
        core.output(currentStatsText);
        core.output("新增 " + newLabelsThisSecond + " 个标签");
    } else if (currentTime % 5 === 0 || currentTime === 0) {
        // 每5秒输出一次基本信息
        core.output("=== UTC时间显示 (第" + currentTime + "秒) ===");
        core.output(timeDisplay);
        core.output(countdownText);
        core.output(progressText);
        core.output(labelProgressText);
    }

    // 等待1秒（代表10分钟实际时间）
    core.wait(1);
}

core.output("UTC时间显示完成");
core.output("所有" + fitData.length + "个文件均已按时间顺序显示");
