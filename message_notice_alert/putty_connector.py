#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PuTTY连接器 - 使用PuTTY连接到远程服务器并执行命令
"""

import json
import subprocess
import os
import time
import tempfile
from pathlib import Path

class PuTTYConnector:
    def __init__(self, config_file="putty_config.json"):
        """
        初始化PuTTY连接器
        
        Args:
            config_file (str): 配置文件路径
        """
        self.config_file = config_file
        self.putty_path = "E:/PuTTY/putty.exe"
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"配置加载成功: {config}")
            return config
        except FileNotFoundError:
            print(f"配置文件 {self.config_file} 未找到")
            return None
        except json.JSONDecodeError as e:
            print(f"配置文件格式错误: {e}")
            return None
    
    def check_putty_exists(self):
        """检查PuTTY是否存在"""
        if not os.path.exists(self.putty_path):
            print(f"PuTTY未找到: {self.putty_path}")
            return False
        return True
    
    def create_putty_session_file(self):
        """创建PuTTY会话文件"""
        if not self.config:
            return None
            
        # 创建临时会话文件
        session_content = f"""[Connection]
Close_On_Exit=2
Host={self.config['ip']}
Port={self.config['port']}
Protocol=ssh
UserName={self.config['user']}

[SSH]
AuthTIS=0
AuthKI=1
AuthGSSAPI=0
GSSLibs=gssapi32,sspi,custom
GSSCustom=
Cipher=aes,blowfish,3des,WARN,arcfour,des
KEX=dh-group14-sha1,dh-group1-sha1,rsa,WARN,dh-gss-group14-sha1,dh-gss-group1-sha1
HostKey=rsa,dsa,ecdsa,ed25519,WARN

[Terminal]
LocalEcho=2
LocalEdit=2
"""
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.session', delete=False, encoding='utf-8')
        temp_file.write(session_content)
        temp_file.close()
        
        return temp_file.name
    
    def connect_and_execute(self, command="ls"):
        """
        连接到服务器并执行命令
        
        Args:
            command (str): 要执行的命令，默认为ls
        """
        if not self.check_putty_exists():
            return False
            
        if not self.config:
            print("配置文件加载失败")
            return False
        
        try:
            # 构建PuTTY命令
            putty_cmd = [
                self.putty_path,
                "-ssh",
                f"{self.config['user']}@{self.config['ip']}",
                "-P", str(self.config['port']),
                "-pw", self.config['pass'],
                "-m", "-"  # 从标准输入读取命令
            ]
            
            print(f"正在连接到 {self.config['ip']}:{self.config['port']}...")
            print(f"用户名: {self.config['user']}")
            
            # 启动PuTTY进程
            process = subprocess.Popen(
                putty_cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            # 发送命令
            commands_to_send = f"{command}\nexit\n"
            stdout, stderr = process.communicate(input=commands_to_send, timeout=30)
            
            if process.returncode == 0:
                print("连接成功！")
                print(f"执行命令: {command}")
                print("输出结果:")
                print(stdout)
                return True
            else:
                print(f"连接失败，返回码: {process.returncode}")
                if stderr:
                    print(f"错误信息: {stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("连接超时")
            process.kill()
            return False
        except Exception as e:
            print(f"连接过程中发生错误: {e}")
            return False
    
    def connect_interactive(self):
        """
        启动交互式PuTTY会话
        """
        if not self.check_putty_exists():
            return False
            
        if not self.config:
            print("配置文件加载失败")
            return False
        
        try:
            # 构建PuTTY命令（交互式）
            putty_cmd = [
                self.putty_path,
                "-ssh",
                f"{self.config['user']}@{self.config['ip']}",
                "-P", str(self.config['port']),
                "-pw", self.config['pass']
            ]
            
            print(f"启动交互式连接到 {self.config['ip']}:{self.config['port']}...")
            print(f"用户名: {self.config['user']}")
            print("PuTTY窗口将会打开...")
            
            # 启动PuTTY（非阻塞）
            subprocess.Popen(putty_cmd)
            return True
            
        except Exception as e:
            print(f"启动PuTTY时发生错误: {e}")
            return False

def main():
    """主函数"""
    print("=== PuTTY连接器 ===")
    
    # 创建连接器实例
    connector = PuTTYConnector()
    
    # 选择连接方式
    print("\n请选择连接方式:")
    print("1. 自动连接并执行ls命令")
    print("2. 启动交互式PuTTY会话")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        # 自动连接并执行ls命令
        success = connector.connect_and_execute("ls")
        if success:
            print("\n命令执行完成")
        else:
            print("\n命令执行失败")
    elif choice == "2":
        # 启动交互式会话
        success = connector.connect_interactive()
        if success:
            print("\nPuTTY会话已启动")
        else:
            print("\n启动PuTTY会话失败")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
