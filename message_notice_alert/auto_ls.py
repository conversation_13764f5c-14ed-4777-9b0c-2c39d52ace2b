#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动连接PuTTY并执行ls命令
"""

import json
import subprocess
import os
import tempfile

def load_config(config_file="putty_config.json"):
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"配置加载成功: IP={config['ip']}, 用户={config['user']}, 端口={config['port']}")
        return config
    except Exception as e:
        print(f"配置文件加载失败: {e}")
        return None

def execute_ls_command():
    """执行ls命令"""
    config = load_config()
    if not config:
        return False
    
    putty_path = "E:/PuTTY/putty.exe"
    
    # 检查PuTTY是否存在
    if not os.path.exists(putty_path):
        print(f"PuTTY未找到: {putty_path}")
        return False
    
    try:
        # 创建临时脚本文件
        script_content = "ls\nexit\n"
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(script_content)
            script_file = temp_file.name
        
        # 构建PuTTY命令
        putty_cmd = [
            putty_path,
            "-ssh",
            f"{config['user']}@{config['ip']}",
            "-P", str(config['port']),
            "-pw", config['pass'],
            "-m", script_file
        ]
        
        print(f"正在连接到 {config['ip']}:{config['port']}...")
        print("执行ls命令...")
        
        # 执行命令
        result = subprocess.run(
            putty_cmd,
            capture_output=True,
            text=True,
            timeout=30,
            encoding='utf-8'
        )
        
        # 清理临时文件
        try:
            os.unlink(script_file)
        except:
            pass
        
        if result.returncode == 0:
            print("连接成功！ls命令执行结果:")
            print("-" * 50)
            print(result.stdout)
            print("-" * 50)
            return True
        else:
            print(f"执行失败，返回码: {result.returncode}")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("连接超时")
        return False
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    print("=== 自动执行ls命令 ===")
    success = execute_ls_command()
    if success:
        print("\n任务完成")
    else:
        print("\n任务失败")
    
    input("按回车键退出...")
