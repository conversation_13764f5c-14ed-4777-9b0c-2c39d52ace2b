
 LabelMgr.deleteAllLabels();

// 动态颜色效果 分别代表开始， 持续， 结束
var colors = ["#ff0000", "#0000ff", "#00ff00"];
var colorIndex = 0;

// 使用循环和延时来实现动态效果
for (var i = 0; i < 60; i++) {
    colorIndex = i % colors.length;

    // 删除旧标签（如果存在）
    LabelMgr.deleteLabel("GY1_K001-2");
    // 创建新颜色的标签（添加系统名称前缀）
    LabelMgr.labelEquatorial("GY1_K001-2", "2.18182h", "+69.0", true, 14, colors[colorIndex]);

    // 等待1秒
    core.wait(0.3);
}

