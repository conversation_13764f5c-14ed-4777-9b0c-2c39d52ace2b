// Stellarium 脚本 - 可调用的天球坐标方框绘制工具
// 作者: 通知服务系统
// 用途: 提供可重复调用的方框绘制函数

// 脚本开始
core.debug("加载天球坐标方框绘制工具");

// 设置初始环境
core.setTimeRate(1);
core.setDate("2024-01-01T20:00:00");

// 清除所有现有标记
core.clear("ScreenImages");

// ========================================
// 可调用的方框绘制函数
// ========================================

/**
 * 在指定天球坐标绘制方框
 * @param {number} ra - 赤经 (度数, 0-360)
 * @param {number} dec - 赤纬 (度数, -90到+90)
 * @param {number} size - 方框大小 (度数)
 * @param {string} color - 方框颜色 (十六进制, 如 "#FF6B6B")
 * @param {string} title - 方框标题
 * @param {string} subtitle - 副标题 (可选)
 */
function drawBoxAtCoordinates(ra, dec, size, color, title, subtitle) {
    core.debug("绘制方框: " + title + " 在坐标 RA=" + ra + "° DEC=" + dec + "°");

    var halfSize = size / 2;

    // 显示标题和坐标信息
    LabelMgr.labelEquatorial(title, ra, dec, true, 20, color);
    if (subtitle) {
        LabelMgr.labelEquatorial(subtitle, ra, dec - 1, true, 14, "#FFFFFF");
    }
    LabelMgr.labelEquatorial("RA:" + ra + "° DEC:" + dec + "°", ra, dec - 2, true, 12, "#CCCCCC");

    // 绘制方框的四个角
    LabelMgr.labelEquatorial("┌", ra - halfSize, dec + halfSize, true, 18, color);
    LabelMgr.labelEquatorial("┐", ra + halfSize, dec + halfSize, true, 18, color);
    LabelMgr.labelEquatorial("└", ra - halfSize, dec - halfSize, true, 18, color);
    LabelMgr.labelEquatorial("┘", ra + halfSize, dec - halfSize, true, 18, color);

    // 绘制水平边
    var steps = Math.max(3, Math.floor(size));
    for(var i = 1; i < steps; i++) {
        var raOffset = (ra - halfSize) + (size * i / steps);
        LabelMgr.labelEquatorial("─", raOffset, dec + halfSize, true, 14, color);
        LabelMgr.labelEquatorial("─", raOffset, dec - halfSize, true, 14, color);
    }

    // 绘制垂直边
    for(var j = 1; j < steps; j++) {
        var decOffset = (dec - halfSize) + (size * j / steps);
        LabelMgr.labelEquatorial("│", ra - halfSize, decOffset, true, 14, color);
        LabelMgr.labelEquatorial("│", ra + halfSize, decOffset, true, 14, color);
    }

    // 显示中心十字准星
    LabelMgr.labelEquatorial("✚", ra, dec, true, 16, "#FFFF00");

    return true;
}

/**
 * 移动视角到指定坐标并绘制方框
 * @param {number} ra - 赤经 (度数)
 * @param {number} dec - 赤纬 (度数)
 * @param {number} size - 方框大小 (度数)
 * @param {string} color - 方框颜色
 * @param {string} title - 方框标题
 * @param {string} subtitle - 副标题
 * @param {number} waitTime - 等待时间 (秒, 默认3秒)
 */
function moveToAndDrawBox(ra, dec, size, color, title, subtitle, waitTime) {
    waitTime = waitTime || 3;

    core.debug("移动到坐标并绘制方框: " + title);

    // 移动视角
    core.moveToRaDec(ra, dec);
    core.wait(waitTime);

    // 绘制方框
    drawBoxAtCoordinates(ra, dec, size, color, title, subtitle);

    return true;
}

/**
 * 批量绘制多个方框
 * @param {Array} boxList - 方框配置数组
 * 每个元素格式: {ra: number, dec: number, size: number, color: string, title: string, subtitle: string}
 */
function drawMultipleBoxes(boxList) {
    core.debug("批量绘制 " + boxList.length + " 个方框");

    for(var i = 0; i < boxList.length; i++) {
        var box = boxList[i];
        drawBoxAtCoordinates(
            box.ra,
            box.dec,
            box.size || 3,
            box.color || "#FFFFFF",
            box.title || "方框" + (i+1),
            box.subtitle
        );
    }

    return true;
}

// ========================================
// 使用示例和演示
// ========================================

// 显示欢迎信息
LabelMgr.labelScreen("天球坐标方框绘制工具", 400, 50, true, 20, "#FFFFFF");
LabelMgr.labelScreen("可调用函数演示", 400, 80, true, 16, "#CCCCCC");

core.debug("开始函数调用演示");

// 等待2秒
core.wait(2);

// ========================================
// 示例1: 单个方框绘制
// ========================================

core.debug("示例1: 使用 drawBoxAtCoordinates 函数");
LabelMgr.labelScreen("示例1: 绘制单个方框", 400, 120, true, 16, "#FFFF00");

// 调用方框绘制函数
drawBoxAtCoordinates(90, 30, 4, "#FF6B6B", "双子座方框", "示例方框1");

core.wait(3);

// ========================================
// 示例2: 移动视角并绘制方框
// ========================================

core.debug("示例2: 使用 moveToAndDrawBox 函数");
LabelMgr.labelScreen("示例2: 移动视角并绘制方框", 400, 150, true, 16, "#FFFF00");

// 调用移动并绘制函数
moveToAndDrawBox(180, -20, 5, "#4ECDC4", "室女座方框", "示例方框2", 3);

core.wait(3);

// 等待5秒让用户观察
core.wait(5);

// ========================================
// 示例3: 批量绘制多个方框
// ========================================

core.debug("示例3: 使用 drawMultipleBoxes 函数");
LabelMgr.labelScreen("示例3: 批量绘制多个方框", 400, 180, true, 16, "#FFFF00");

// 定义方框配置数组
var boxConfigs = [
    {
        ra: 0,
        dec: 0,
        size: 3,
        color: "#FF6B6B",
        title: "春分点",
        subtitle: "赤道与黄道交点"
    },
    {
        ra: 270,
        dec: 45,
        size: 4,
        color: "#96CEB4",
        title: "天鹅座",
        subtitle: "北十字星座"
    },
    {
        ra: 315,
        dec: -15,
        size: 3.5,
        color: "#FFEAA7",
        title: "宝瓶座",
        subtitle: "黄道星座"
    }
];

// 批量绘制方框
drawMultipleBoxes(boxConfigs);

core.wait(5);

// ========================================
// 自定义调用示例
// ========================================

core.debug("自定义调用示例");
LabelMgr.labelScreen("自定义调用示例", 400, 210, true, 16, "#FFFF00");

// 你可以直接调用这些函数来绘制自定义方框:

// 示例: 在北极星附近绘制方框
drawBoxAtCoordinates(37.95, 89.26, 2, "#FF0000", "北极星", "极星标记");

// 示例: 移动到天狼星并绘制方框
moveToAndDrawBox(101.287, -16.716, 3, "#00FFFF", "天狼星", "最亮恒星", 2);

core.wait(3);

// 脚本结束
LabelMgr.labelScreen("函数演示完成", 400, 600, true, 20, "#00FF00");
LabelMgr.labelScreen("可调用函数已加载完毕", 400, 630, true, 16, "#FFFFFF");

core.debug("天球坐标方框绘制工具加载完成");

// ========================================
// 函数使用说明
// ========================================

/*
可用函数:

1. drawBoxAtCoordinates(ra, dec, size, color, title, subtitle)
   - 在指定坐标绘制方框
   - ra: 赤经 (度数, 0-360)
   - dec: 赤纬 (度数, -90到+90)
   - size: 方框大小 (度数)
   - color: 颜色 (十六进制, 如 "#FF6B6B")
   - title: 标题
   - subtitle: 副标题 (可选)

2. moveToAndDrawBox(ra, dec, size, color, title, subtitle, waitTime)
   - 移动视角到坐标并绘制方框
   - 参数同上，额外的 waitTime: 等待时间 (秒)

3. drawMultipleBoxes(boxList)
   - 批量绘制多个方框
   - boxList: 方框配置数组

使用示例:
drawBoxAtCoordinates(90, 30, 4, "#FF6B6B", "我的方框", "自定义标记");
moveToAndDrawBox(180, -20, 5, "#4ECDC4", "目标区域", "重要位置", 3);

坐标转换:
- 赤经小时制转度数: 小时 × 15
- 例如: 6小时 = 90度, 12小时 = 180度

常见坐标:
- 春分点: RA=0°, DEC=0°
- 夏至点: RA=90°, DEC=23.5°
- 秋分点: RA=180°, DEC=0°
- 冬至点: RA=270°, DEC=-23.5°
- 北极星: RA=37.95°, DEC=89.26°
- 天狼星: RA=101.287°, DEC=-16.716°
*/
