// Stellarium 脚本：显示日志中 FIT 文件处理状态
// 自动生成于: 2025-08-31 17:24:47
// 时间尺度：0.3秒 = 1分钟实际时间
// 数据来源：日志文件分析结果

LabelMgr.deleteAllLabels();

// 状态颜色定义
var colors = {
    idle: "#666666",      // 灰色 - 空闲
    waiting: "#ffff00",   // 黄色 - 等待
    processing: "#ff8800", // 橙色 - 处理中
    completed: "#00ff00",  // 绿色 - 完成
    error: "#ff0000"      // 红色 - 错误
};

// FIT文件处理时间线（从日志提取）
var fitTimeline = [
    {
        filename: "GY1_K052-1_No Filter_60S_Bin2_UTC20250826_151830_-15C_.fit",
        region: "K052-1",
        system: "GY1",
        ra: "17.69310h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:18:30",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-15C_.fit",
        region: "K052-1",
        system: "GY1",
        ra: "17.69310h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-1_No Filter_60S_Bin2_UTC20250826_155155_-14.9C_.fit",
        region: "K052-1",
        system: "GY1",
        ra: "17.69310h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:51:55",
        startTime: 59,
        duration: 34
    },
    {
        filename: "GY1_K052-2_No Filter_60S_Bin2_UTC20250826_152021_-14.9C_.fit",
        region: "K052-2",
        system: "GY1",
        ra: "18.10690h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:20:21",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-2_No Filter_60S_Bin2_UTC20250826_153703_-15C_.fit",
        region: "K052-2",
        system: "GY1",
        ra: "18.10690h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:37:03",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-2_No Filter_60S_Bin2_UTC20250826_155345_-15C_.fit",
        region: "K052-2",
        system: "GY1",
        ra: "18.10690h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:53:45",
        startTime: 59,
        duration: 34
    },
    {
        filename: "GY1_K052-3_No Filter_60S_Bin2_UTC20250826_152212_-15C_.fit",
        region: "K052-3",
        system: "GY1",
        ra: "18.52069h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:22:12",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-3_No Filter_60S_Bin2_UTC20250826_153853_-15C_.fit",
        region: "K052-3",
        system: "GY1",
        ra: "18.52069h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:38:53",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-3_No Filter_60S_Bin2_UTC20250826_155534_-14.9C_.fit",
        region: "K052-3",
        system: "GY1",
        ra: "18.52069h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:55:34",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY1_K052-4_No Filter_60S_Bin2_UTC20250826_152406_-14.9C_.fit",
        region: "K052-4",
        system: "GY1",
        ra: "17.58421h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:24:06",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-4_No Filter_60S_Bin2_UTC20250826_154049_-15C_.fit",
        region: "K052-4",
        system: "GY1",
        ra: "17.58421h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:40:49",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-4_No Filter_60S_Bin2_UTC20250826_155730_-15C_.fit",
        region: "K052-4",
        system: "GY1",
        ra: "17.58421h",
        dec: "+21.98",
        utcTime: "2025-08-26 15:57:30",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY1_K052-5_No Filter_60S_Bin2_UTC20250826_152555_-15C_.fit",
        region: "K052-5",
        system: "GY1",
        ra: "18.00526h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:25:55",
        startTime: 0,
        duration: 36
    },
    {
        filename: "GY1_K052-5_No Filter_60S_Bin2_UTC20250826_154238_-15C_.fit",
        region: "K052-5",
        system: "GY1",
        ra: "18.00526h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:42:38",
        startTime: 0,
        duration: 36
    },
    {
        filename: "GY1_K052-5_No Filter_60S_Bin2_UTC20250826_155919_-14.9C_.fit",
        region: "K052-5",
        system: "GY1",
        ra: "18.00526h",
        dec: "+21.98",
        utcTime: "2025-08-26 15:59:19",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K052-6_No Filter_60S_Bin2_UTC20250826_152746_-15C_.fit",
        region: "K052-6",
        system: "GY1",
        ra: "18.42632h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:27:46",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-6_No Filter_60S_Bin2_UTC20250826_154426_-15C_.fit",
        region: "K052-6",
        system: "GY1",
        ra: "18.42632h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:44:26",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-6_No Filter_60S_Bin2_UTC20250826_160108_-14.9C_.fit",
        region: "K052-6",
        system: "GY1",
        ra: "18.42632h",
        dec: "+21.98",
        utcTime: "2025-08-26 16:01:08",
        startTime: 60,
        duration: 35
    },
    {
        filename: "GY1_K052-7_No Filter_60S_Bin2_UTC20250826_152940_-15C_.fit",
        region: "K052-7",
        system: "GY1",
        ra: "17.67778h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:29:40",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-7_No Filter_60S_Bin2_UTC20250826_154621_-15C_.fit",
        region: "K052-7",
        system: "GY1",
        ra: "17.67778h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:46:21",
        startTime: 0,
        duration: 34
    },
    {
        filename: "GY1_K052-7_No Filter_60S_Bin2_UTC20250826_160304_-15C_.fit",
        region: "K052-7",
        system: "GY1",
        ra: "17.67778h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:03:04",
        startTime: 59,
        duration: 34
    },
    {
        filename: "GY1_K052-8_No Filter_60S_Bin2_UTC20250826_153130_-15C_.fit",
        region: "K052-8",
        system: "GY1",
        ra: "18.12222h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:31:30",
        startTime: 0,
        duration: 34
    },
    {
        filename: "GY1_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-14.9C_.fit",
        region: "K052-8",
        system: "GY1",
        ra: "18.12222h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: 0,
        duration: 31
    },
    {
        filename: "GY1_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-15C_.fit",
        region: "K052-8",
        system: "GY1",
        ra: "18.12222h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: 59,
        duration: 34
    },
    {
        filename: "GY1_K052-9_No Filter_60S_Bin2_UTC20250826_153320_-15C_.fit",
        region: "K052-9",
        system: "GY1",
        ra: "18.56667h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:33:20",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-9_No Filter_60S_Bin2_UTC20250826_155001_-14.9C_.fit",
        region: "K052-9",
        system: "GY1",
        ra: "18.56667h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:50:01",
        startTime: 59,
        duration: 30
    },
    {
        filename: "GY1_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY1",
        ra: "18.56667h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: 59,
        duration: 36
    },
    {
        filename: "GY1_K074-1_No Filter_60S_Bin2_UTC20250826_160847_-15C_.fit",
        region: "K074-1",
        system: "GY1",
        ra: "19.50000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:08:47",
        startTime: 60,
        duration: 35
    },
    {
        filename: "GY1_K074-1_No Filter_60S_Bin2_UTC20250826_162529_-15C_.fit",
        region: "K074-1",
        system: "GY1",
        ra: "19.50000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:25:29",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY1_K074-1_No Filter_60S_Bin2_UTC20250826_164210_-15C_.fit",
        region: "K074-1",
        system: "GY1",
        ra: "19.50000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:42:10",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-2_No Filter_60S_Bin2_UTC20250826_161036_-14.9C_.fit",
        region: "K074-2",
        system: "GY1",
        ra: "19.90000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:10:36",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-2_No Filter_60S_Bin2_UTC20250826_162720_-14.9C_.fit",
        region: "K074-2",
        system: "GY1",
        ra: "19.90000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:27:20",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-2_No Filter_60S_Bin2_UTC20250826_164400_-15C_.fit",
        region: "K074-2",
        system: "GY1",
        ra: "19.90000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:44:00",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-3_No Filter_60S_Bin2_UTC20250826_161226_-14.9C_.fit",
        region: "K074-3",
        system: "GY1",
        ra: "20.30000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:12:26",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-3_No Filter_60S_Bin2_UTC20250826_162907_-14.9C_.fit",
        region: "K074-3",
        system: "GY1",
        ra: "20.30000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:29:07",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-3_No Filter_60S_Bin2_UTC20250826_164550_-15C_.fit",
        region: "K074-3",
        system: "GY1",
        ra: "20.30000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:45:50",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-4_No Filter_60S_Bin2_UTC20250826_161420_-15C_.fit",
        region: "K074-4",
        system: "GY1",
        ra: "19.50000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:14:20",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY1_K074-4_No Filter_60S_Bin2_UTC20250826_163102_-15C_.fit",
        region: "K074-4",
        system: "GY1",
        ra: "19.50000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:31:02",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY1_K074-4_No Filter_60S_Bin2_UTC20250826_164743_-14.9C_.fit",
        region: "K074-4",
        system: "GY1",
        ra: "19.50000h",
        dec: "+3.98",
        utcTime: "2025-08-26 16:47:43",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY1_K074-5_No Filter_60S_Bin2_UTC20250826_161612_-15C_.fit",
        region: "K074-5",
        system: "GY1",
        ra: "19.90000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:16:12",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY1_K074-5_No Filter_60S_Bin2_UTC20250826_163252_-14.9C_.fit",
        region: "K074-5",
        system: "GY1",
        ra: "19.90000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:32:52",
        startTime: 60,
        duration: 35
    },
    {
        filename: "GY1_K074-5_No Filter_60S_Bin2_UTC20250826_164932_-15C_.fit",
        region: "K074-5",
        system: "GY1",
        ra: "19.90000h",
        dec: "+3.98",
        utcTime: "2025-08-26 16:49:32",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-6_No Filter_60S_Bin2_UTC20250826_161759_-14.9C_.fit",
        region: "K074-6",
        system: "GY1",
        ra: "20.30000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:17:59",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-6_No Filter_60S_Bin2_UTC20250826_163441_-14.9C_.fit",
        region: "K074-6",
        system: "GY1",
        ra: "20.30000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:34:41",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-6_No Filter_60S_Bin2_UTC20250826_165121_-14.9C_.fit",
        region: "K074-6",
        system: "GY1",
        ra: "20.30000h",
        dec: "+3.98",
        utcTime: "2025-08-26 16:51:21",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY1_K074-7_No Filter_60S_Bin2_UTC20250826_161955_-15C_.fit",
        region: "K074-7",
        system: "GY1",
        ra: "19.50000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:19:55",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY1_K074-7_No Filter_60S_Bin2_UTC20250826_163636_-15C_.fit",
        region: "K074-7",
        system: "GY1",
        ra: "19.50000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:36:36",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY1_K074-8_No Filter_60S_Bin2_UTC20250826_162145_-14.9C_.fit",
        region: "K074-8",
        system: "GY1",
        ra: "19.90000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:21:45",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY1_K074-8_No Filter_60S_Bin2_UTC20250826_163826_-15C_.fit",
        region: "K074-8",
        system: "GY1",
        ra: "19.90000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:38:26",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY1_K074-9_No Filter_60S_Bin2_UTC20250826_162336_-14.9C_.fit",
        region: "K074-9",
        system: "GY1",
        ra: "20.30000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:23:36",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY1_K074-9_No Filter_60S_Bin2_UTC20250826_164016_-15C_.fit",
        region: "K074-9",
        system: "GY1",
        ra: "20.30000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:40:16",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K052-1_No Filter_60S_Bin2_UTC20250826_151830_-15C_.fit",
        region: "K052-1",
        system: "GY2",
        ra: "17.89310h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:18:30",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-14.9C_.fit",
        region: "K052-1",
        system: "GY2",
        ra: "17.89310h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-1_No Filter_60S_Bin2_UTC20250826_155155_-15C_.fit",
        region: "K052-1",
        system: "GY2",
        ra: "17.89310h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:51:55",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K052-2_No Filter_60S_Bin2_UTC20250826_152021_-15C_.fit",
        region: "K052-2",
        system: "GY2",
        ra: "18.30690h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:20:21",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-2_No Filter_60S_Bin2_UTC20250826_153702_-15C_.fit",
        region: "K052-2",
        system: "GY2",
        ra: "18.30690h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:37:02",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-2_No Filter_60S_Bin2_UTC20250826_155345_-15C_.fit",
        region: "K052-2",
        system: "GY2",
        ra: "18.30690h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:53:45",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-3_No Filter_60S_Bin2_UTC20250826_152211_-14.9C_.fit",
        region: "K052-3",
        system: "GY2",
        ra: "18.72069h",
        dec: "+16.0",
        utcTime: "2025-08-26 15:22:11",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-3_No Filter_60S_Bin2_UTC20250826_153853_-15C_.fit",
        region: "K052-3",
        system: "GY2",
        ra: "18.72069h",
        dec: "+15.99",
        utcTime: "2025-08-26 15:38:53",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-3_No Filter_60S_Bin2_UTC20250826_155534_-15C_.fit",
        region: "K052-3",
        system: "GY2",
        ra: "18.72069h",
        dec: "+15.98",
        utcTime: "2025-08-26 15:55:34",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-4_No Filter_60S_Bin2_UTC20250826_152406_-15C_.fit",
        region: "K052-4",
        system: "GY2",
        ra: "17.78421h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:24:06",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-4_No Filter_60S_Bin2_UTC20250826_154048_-15C_.fit",
        region: "K052-4",
        system: "GY2",
        ra: "17.78421h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:40:48",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K052-4_No Filter_60S_Bin2_UTC20250826_155730_-15C_.fit",
        region: "K052-4",
        system: "GY2",
        ra: "17.78421h",
        dec: "+21.98",
        utcTime: "2025-08-26 15:57:30",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K052-5_No Filter_60S_Bin2_UTC20250826_152555_-15C_.fit",
        region: "K052-5",
        system: "GY2",
        ra: "18.20526h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:25:55",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-5_No Filter_60S_Bin2_UTC20250826_154237_-14.9C_.fit",
        region: "K052-5",
        system: "GY2",
        ra: "18.20526h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:42:37",
        startTime: 0,
        duration: 32
    },
    {
        filename: "GY2_K052-5_No Filter_60S_Bin2_UTC20250826_155920_-15C_.fit",
        region: "K052-5",
        system: "GY2",
        ra: "18.20526h",
        dec: "+21.98",
        utcTime: "2025-08-26 15:59:20",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K052-6_No Filter_60S_Bin2_UTC20250826_152745_-15C_.fit",
        region: "K052-6",
        system: "GY2",
        ra: "18.62632h",
        dec: "+22.0",
        utcTime: "2025-08-26 15:27:45",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-6_No Filter_60S_Bin2_UTC20250826_154427_-15C_.fit",
        region: "K052-6",
        system: "GY2",
        ra: "18.62632h",
        dec: "+21.99",
        utcTime: "2025-08-26 15:44:27",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-6_No Filter_60S_Bin2_UTC20250826_160109_-14.9C_.fit",
        region: "K052-6",
        system: "GY2",
        ra: "18.62632h",
        dec: "+21.98",
        utcTime: "2025-08-26 16:01:09",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-7_No Filter_60S_Bin2_UTC20250826_152941_-15C_.fit",
        region: "K052-7",
        system: "GY2",
        ra: "17.87778h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:29:41",
        startTime: 0,
        duration: 30
    },
    {
        filename: "GY2_K052-7_No Filter_60S_Bin2_UTC20250826_154621_-14.9C_.fit",
        region: "K052-7",
        system: "GY2",
        ra: "17.87778h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:46:21",
        startTime: 0,
        duration: 30
    },
    {
        filename: "GY2_K052-7_No Filter_60S_Bin2_UTC20250826_160304_-15C_.fit",
        region: "K052-7",
        system: "GY2",
        ra: "17.87778h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:03:04",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K052-8_No Filter_60S_Bin2_UTC20250826_153129_-15C_.fit",
        region: "K052-8",
        system: "GY2",
        ra: "18.32222h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:31:29",
        startTime: 0,
        duration: 30
    },
    {
        filename: "GY2_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-14.9C_.fit",
        region: "K052-8",
        system: "GY2",
        ra: "18.32222h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: 0,
        duration: 31
    },
    {
        filename: "GY2_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-14.9C_.fit",
        region: "K052-8",
        system: "GY2",
        ra: "18.32222h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY2_K052-9_No Filter_60S_Bin2_UTC20250826_153320_-15C_.fit",
        region: "K052-9",
        system: "GY2",
        ra: "18.76667h",
        dec: "+28.0",
        utcTime: "2025-08-26 15:33:20",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-9_No Filter_60S_Bin2_UTC20250826_155000_-15C_.fit",
        region: "K052-9",
        system: "GY2",
        ra: "18.76667h",
        dec: "+27.99",
        utcTime: "2025-08-26 15:50:00",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY2",
        ra: "18.76667h",
        dec: "+27.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-1_No Filter_60S_Bin2_UTC20250826_160848_-14.9C_.fit",
        region: "K074-1",
        system: "GY2",
        ra: "19.70000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:08:48",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K074-1_No Filter_60S_Bin2_UTC20250826_162530_-15C_.fit",
        region: "K074-1",
        system: "GY2",
        ra: "19.70000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:25:30",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY2_K074-1_No Filter_60S_Bin2_UTC20250826_164211_-14.9C_.fit",
        region: "K074-1",
        system: "GY2",
        ra: "19.70000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:42:11",
        startTime: 60,
        duration: 40
    },
    {
        filename: "GY2_K074-2_No Filter_60S_Bin2_UTC20250826_161037_-14.9C_.fit",
        region: "K074-2",
        system: "GY2",
        ra: "20.10000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:10:37",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K074-2_No Filter_60S_Bin2_UTC20250826_162718_-14.9C_.fit",
        region: "K074-2",
        system: "GY2",
        ra: "20.10000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:27:18",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K074-2_No Filter_60S_Bin2_UTC20250826_164359_-14.9C_.fit",
        region: "K074-2",
        system: "GY2",
        ra: "20.10000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:43:59",
        startTime: 60,
        duration: 32
    },
    {
        filename: "GY2_K074-3_No Filter_60S_Bin2_UTC20250826_161225_-15C_.fit",
        region: "K074-3",
        system: "GY2",
        ra: "20.50000h",
        dec: "-2.0",
        utcTime: "2025-08-26 16:12:25",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K074-3_No Filter_60S_Bin2_UTC20250826_162907_-14.9C_.fit",
        region: "K074-3",
        system: "GY2",
        ra: "20.50000h",
        dec: "-2.01",
        utcTime: "2025-08-26 16:29:07",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K074-3_No Filter_60S_Bin2_UTC20250826_164550_-15C_.fit",
        region: "K074-3",
        system: "GY2",
        ra: "20.50000h",
        dec: "-2.02",
        utcTime: "2025-08-26 16:45:50",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-4_No Filter_60S_Bin2_UTC20250826_161421_-15C_.fit",
        region: "K074-4",
        system: "GY2",
        ra: "19.70000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:14:21",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-4_No Filter_60S_Bin2_UTC20250826_163103_-15C_.fit",
        region: "K074-4",
        system: "GY2",
        ra: "19.70000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:31:03",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-4_No Filter_60S_Bin2_UTC20250826_164743_-15C_.fit",
        region: "K074-4",
        system: "GY2",
        ra: "19.70000h",
        dec: "+3.98",
        utcTime: "2025-08-26 16:47:43",
        startTime: 60,
        duration: 38
    },
    {
        filename: "GY2_K074-5_No Filter_60S_Bin2_UTC20250826_161611_-15C_.fit",
        region: "K074-5",
        system: "GY2",
        ra: "20.10000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:16:11",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-5_No Filter_60S_Bin2_UTC20250826_163252_-14.9C_.fit",
        region: "K074-5",
        system: "GY2",
        ra: "20.10000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:32:52",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-5_No Filter_60S_Bin2_UTC20250826_164932_-15C_.fit",
        region: "K074-5",
        system: "GY2",
        ra: "20.10000h",
        dec: "+3.98",
        utcTime: "2025-08-26 16:49:32",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-6_No Filter_60S_Bin2_UTC20250826_161759_-15C_.fit",
        region: "K074-6",
        system: "GY2",
        ra: "20.50000h",
        dec: "+4.0",
        utcTime: "2025-08-26 16:17:59",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K074-6_No Filter_60S_Bin2_UTC20250826_163442_-15C_.fit",
        region: "K074-6",
        system: "GY2",
        ra: "20.50000h",
        dec: "+3.99",
        utcTime: "2025-08-26 16:34:42",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY2_K074-7_No Filter_60S_Bin2_UTC20250826_161955_-15C_.fit",
        region: "K074-7",
        system: "GY2",
        ra: "19.70000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:19:55",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-7_No Filter_60S_Bin2_UTC20250826_163636_-15C_.fit",
        region: "K074-7",
        system: "GY2",
        ra: "19.70000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:36:36",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-8_No Filter_60S_Bin2_UTC20250826_162145_-15C_.fit",
        region: "K074-8",
        system: "GY2",
        ra: "20.10000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:21:45",
        startTime: 60,
        duration: 34
    },
    {
        filename: "GY2_K074-8_No Filter_60S_Bin2_UTC20250826_163825_-15C_.fit",
        region: "K074-8",
        system: "GY2",
        ra: "20.10000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:38:25",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-9_No Filter_60S_Bin2_UTC20250826_162336_-14.9C_.fit",
        region: "K074-9",
        system: "GY2",
        ra: "20.50000h",
        dec: "+10.0",
        utcTime: "2025-08-26 16:23:36",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY2_K074-9_No Filter_60S_Bin2_UTC20250826_164015_-15C_.fit",
        region: "K074-9",
        system: "GY2",
        ra: "20.50000h",
        dec: "+9.99",
        utcTime: "2025-08-26 16:40:15",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY3_K052-1_No Filter_60S_Bin2_UTC20250826_151831_-14.9C_.fit",
        region: "K052-1",
        system: "GY3",
        ra: "17.69310h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:18:31",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY3_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-15C_.fit",
        region: "K052-1",
        system: "GY3",
        ra: "17.69310h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY3_K052-1_No Filter_60S_Bin2_UTC20250826_155155_-15C_.fit",
        region: "K052-1",
        system: "GY3",
        ra: "17.69310h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:51:55",
        startTime: 62,
        duration: 30
    },
    {
        filename: "GY3_K052-2_No Filter_60S_Bin2_UTC20250826_152021_-15C_.fit",
        region: "K052-2",
        system: "GY3",
        ra: "18.10690h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:20:21",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY3_K052-2_No Filter_60S_Bin2_UTC20250826_153703_-15C_.fit",
        region: "K052-2",
        system: "GY3",
        ra: "18.10690h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:37:03",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY3_K052-2_No Filter_60S_Bin2_UTC20250826_155346_-15C_.fit",
        region: "K052-2",
        system: "GY3",
        ra: "18.10690h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:53:46",
        startTime: 62,
        duration: 38
    },
    {
        filename: "GY3_K052-3_No Filter_60S_Bin2_UTC20250826_152212_-15C_.fit",
        region: "K052-3",
        system: "GY3",
        ra: "18.52069h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:22:12",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY3_K052-3_No Filter_60S_Bin2_UTC20250826_153854_-15C_.fit",
        region: "K052-3",
        system: "GY3",
        ra: "18.52069h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:38:54",
        startTime: 61,
        duration: 30
    },
    {
        filename: "GY3_K052-3_No Filter_60S_Bin2_UTC20250826_155534_-14.9C_.fit",
        region: "K052-3",
        system: "GY3",
        ra: "18.52069h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:55:34",
        startTime: 62,
        duration: 30
    },
    {
        filename: "GY3_K052-4_No Filter_60S_Bin2_UTC20250826_152406_-14.9C_.fit",
        region: "K052-4",
        system: "GY3",
        ra: "17.58421h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:24:06",
        startTime: 0,
        duration: 30
    },
    {
        filename: "GY3_K052-4_No Filter_60S_Bin2_UTC20250826_154048_-15C_.fit",
        region: "K052-4",
        system: "GY3",
        ra: "17.58421h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:40:48",
        startTime: 0,
        duration: 30
    },
    {
        filename: "GY3_K052-4_No Filter_60S_Bin2_UTC20250826_155730_-15C_.fit",
        region: "K052-4",
        system: "GY3",
        ra: "17.58421h",
        dec: "+20.98",
        utcTime: "2025-08-26 15:57:30",
        startTime: 63,
        duration: 33
    },
    {
        filename: "GY3_K052-5_No Filter_60S_Bin2_UTC20250826_152556_-14.9C_.fit",
        region: "K052-5",
        system: "GY3",
        ra: "18.00526h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:25:56",
        startTime: 0,
        duration: 32
    },
    {
        filename: "GY3_K052-5_No Filter_60S_Bin2_UTC20250826_154237_-15C_.fit",
        region: "K052-5",
        system: "GY3",
        ra: "18.00526h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:42:37",
        startTime: 63,
        duration: 31
    },
    {
        filename: "GY3_K052-5_No Filter_60S_Bin2_UTC20250826_155920_-14.9C_.fit",
        region: "K052-5",
        system: "GY3",
        ra: "18.00526h",
        dec: "+20.98",
        utcTime: "2025-08-26 15:59:20",
        startTime: 63,
        duration: 33
    },
    {
        filename: "GY3_K052-6_No Filter_60S_Bin2_UTC20250826_152745_-15C_.fit",
        region: "K052-6",
        system: "GY3",
        ra: "18.42632h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:27:45",
        startTime: 62,
        duration: 30
    },
    {
        filename: "GY3_K052-6_No Filter_60S_Bin2_UTC20250826_154426_-15C_.fit",
        region: "K052-6",
        system: "GY3",
        ra: "18.42632h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:44:26",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY3_K052-6_No Filter_60S_Bin2_UTC20250826_160109_-15C_.fit",
        region: "K052-6",
        system: "GY3",
        ra: "18.42632h",
        dec: "+20.98",
        utcTime: "2025-08-26 16:01:09",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY3_K052-7_No Filter_60S_Bin2_UTC20250826_152940_-14.9C_.fit",
        region: "K052-7",
        system: "GY3",
        ra: "17.67778h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:29:40",
        startTime: 0,
        duration: 34
    },
    {
        filename: "GY3_K052-7_No Filter_60S_Bin2_UTC20250826_154621_-14.9C_.fit",
        region: "K052-7",
        system: "GY3",
        ra: "17.67778h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:46:21",
        startTime: 0,
        duration: 35
    },
    {
        filename: "GY3_K052-7_No Filter_60S_Bin2_UTC20250826_160305_-15C_.fit",
        region: "K052-7",
        system: "GY3",
        ra: "17.67778h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:03:05",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY3_K052-8_No Filter_60S_Bin2_UTC20250826_153130_-14.9C_.fit",
        region: "K052-8",
        system: "GY3",
        ra: "18.12222h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:31:30",
        startTime: 0,
        duration: 31
    },
    {
        filename: "GY3_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-15C_.fit",
        region: "K052-8",
        system: "GY3",
        ra: "18.12222h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY3_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-15C_.fit",
        region: "K052-8",
        system: "GY3",
        ra: "18.12222h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: 60,
        duration: 33
    },
    {
        filename: "GY3_K052-9_No Filter_60S_Bin2_UTC20250826_153319_-15C_.fit",
        region: "K052-9",
        system: "GY3",
        ra: "18.56667h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:33:19",
        startTime: 62,
        duration: 30
    },
    {
        filename: "GY3_K052-9_No Filter_60S_Bin2_UTC20250826_155001_-14.9C_.fit",
        region: "K052-9",
        system: "GY3",
        ra: "18.56667h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:50:01",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY3_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY3",
        ra: "18.56667h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: 60,
        duration: 30
    },
    {
        filename: "GY3_K074-1_No Filter_60S_Bin2_UTC20250826_160848_-14.9C_.fit",
        region: "K074-1",
        system: "GY3",
        ra: "19.50000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:08:48",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY3_K074-1_No Filter_60S_Bin2_UTC20250826_162528_-15C_.fit",
        region: "K074-1",
        system: "GY3",
        ra: "19.50000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:25:28",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY3_K074-1_No Filter_60S_Bin2_UTC20250826_164209_-14.9C_.fit",
        region: "K074-1",
        system: "GY3",
        ra: "19.50000h",
        dec: "-3.02",
        utcTime: "2025-08-26 16:42:09",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-2_No Filter_60S_Bin2_UTC20250826_161037_-14.9C_.fit",
        region: "K074-2",
        system: "GY3",
        ra: "19.90000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:10:37",
        startTime: 63,
        duration: 34
    },
    {
        filename: "GY3_K074-2_No Filter_60S_Bin2_UTC20250826_162719_-14.9C_.fit",
        region: "K074-2",
        system: "GY3",
        ra: "19.90000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:27:19",
        startTime: 64,
        duration: 34
    },
    {
        filename: "GY3_K074-2_No Filter_60S_Bin2_UTC20250826_164400_-15C_.fit",
        region: "K074-2",
        system: "GY3",
        ra: "19.90000h",
        dec: "-3.02",
        utcTime: "2025-08-26 16:44:00",
        startTime: 64,
        duration: 34
    },
    {
        filename: "GY3_K074-3_No Filter_60S_Bin2_UTC20250826_161226_-15C_.fit",
        region: "K074-3",
        system: "GY3",
        ra: "20.30000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:12:26",
        startTime: 63,
        duration: 33
    },
    {
        filename: "GY3_K074-3_No Filter_60S_Bin2_UTC20250826_162908_-14.9C_.fit",
        region: "K074-3",
        system: "GY3",
        ra: "20.30000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:29:08",
        startTime: 64,
        duration: 34
    },
    {
        filename: "GY3_K074-3_No Filter_60S_Bin2_UTC20250826_164549_-14.9C_.fit",
        region: "K074-3",
        system: "GY3",
        ra: "20.30000h",
        dec: "-3.02",
        utcTime: "2025-08-26 16:45:49",
        startTime: 64,
        duration: 33
    },
    {
        filename: "GY3_K074-4_No Filter_60S_Bin2_UTC20250826_161422_-14.9C_.fit",
        region: "K074-4",
        system: "GY3",
        ra: "19.50000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:14:22",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY3_K074-4_No Filter_60S_Bin2_UTC20250826_163103_-15C_.fit",
        region: "K074-4",
        system: "GY3",
        ra: "19.50000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:31:03",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-4_No Filter_60S_Bin2_UTC20250826_164743_-15C_.fit",
        region: "K074-4",
        system: "GY3",
        ra: "19.50000h",
        dec: "+2.98",
        utcTime: "2025-08-26 16:47:43",
        startTime: 64,
        duration: 36
    },
    {
        filename: "GY3_K074-5_No Filter_60S_Bin2_UTC20250826_161611_-15C_.fit",
        region: "K074-5",
        system: "GY3",
        ra: "19.90000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:16:11",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY3_K074-5_No Filter_60S_Bin2_UTC20250826_163252_-15C_.fit",
        region: "K074-5",
        system: "GY3",
        ra: "19.90000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:32:52",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-5_No Filter_60S_Bin2_UTC20250826_164932_-15C_.fit",
        region: "K074-5",
        system: "GY3",
        ra: "19.90000h",
        dec: "+2.98",
        utcTime: "2025-08-26 16:49:32",
        startTime: 64,
        duration: 34
    },
    {
        filename: "GY3_K074-6_No Filter_60S_Bin2_UTC20250826_161800_-15C_.fit",
        region: "K074-6",
        system: "GY3",
        ra: "20.30000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:18:00",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-6_No Filter_60S_Bin2_UTC20250826_163442_-15C_.fit",
        region: "K074-6",
        system: "GY3",
        ra: "20.30000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:34:42",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-6_No Filter_60S_Bin2_UTC20250826_165122_-14.9C_.fit",
        region: "K074-6",
        system: "GY3",
        ra: "20.30000h",
        dec: "+2.98",
        utcTime: "2025-08-26 16:51:22",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-7_No Filter_60S_Bin2_UTC20250826_161955_-15C_.fit",
        region: "K074-7",
        system: "GY3",
        ra: "19.50000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:19:55",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-7_No Filter_60S_Bin2_UTC20250826_163636_-14.9C_.fit",
        region: "K074-7",
        system: "GY3",
        ra: "19.50000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:36:36",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-7_No Filter_60S_Bin2_UTC20250826_165316_-14.9C_.fit",
        region: "K074-7",
        system: "GY3",
        ra: "19.50000h",
        dec: "+8.98",
        utcTime: "2025-08-26 16:53:16",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY3_K074-8_No Filter_60S_Bin2_UTC20250826_162145_-15C_.fit",
        region: "K074-8",
        system: "GY3",
        ra: "19.90000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:21:45",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-8_No Filter_60S_Bin2_UTC20250826_163825_-15C_.fit",
        region: "K074-8",
        system: "GY3",
        ra: "19.90000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:38:25",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-8_No Filter_60S_Bin2_UTC20250826_165506_-15C_.fit",
        region: "K074-8",
        system: "GY3",
        ra: "19.90000h",
        dec: "+8.98",
        utcTime: "2025-08-26 16:55:06",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY3_K074-9_No Filter_60S_Bin2_UTC20250826_162335_-15C_.fit",
        region: "K074-9",
        system: "GY3",
        ra: "20.30000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:23:35",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-9_No Filter_60S_Bin2_UTC20250826_164016_-15C_.fit",
        region: "K074-9",
        system: "GY3",
        ra: "20.30000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:40:16",
        startTime: 64,
        duration: 30
    },
    {
        filename: "GY3_K074-9_No Filter_60S_Bin2_UTC20250826_165657_-14.9C_.fit",
        region: "K074-9",
        system: "GY3",
        ra: "20.30000h",
        dec: "+8.98",
        utcTime: "2025-08-26 16:56:57",
        startTime: 63,
        duration: 30
    },
    {
        filename: "GY4_K052-1_No Filter_60S_Bin2_UTC20250826_151831_-15C_.fit",
        region: "K052-1",
        system: "GY4",
        ra: "17.89310h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:18:31",
        startTime: 0,
        duration: 35
    },
    {
        filename: "GY4_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-15C_.fit",
        region: "K052-1",
        system: "GY4",
        ra: "17.89310h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: 0,
        duration: 35
    },
    {
        filename: "GY4_K052-1_No Filter_60S_Bin2_UTC20250826_155155_-15C_.fit",
        region: "K052-1",
        system: "GY4",
        ra: "17.89310h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:51:55",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-2_No Filter_60S_Bin2_UTC20250826_152022_-14.9C_.fit",
        region: "K052-2",
        system: "GY4",
        ra: "18.30690h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:20:22",
        startTime: 66,
        duration: 30
    },
    {
        filename: "GY4_K052-2_No Filter_60S_Bin2_UTC20250826_153703_-15C_.fit",
        region: "K052-2",
        system: "GY4",
        ra: "18.30690h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:37:03",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-2_No Filter_60S_Bin2_UTC20250826_155346_-14.9C_.fit",
        region: "K052-2",
        system: "GY4",
        ra: "18.30690h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:53:46",
        startTime: 68,
        duration: 30
    },
    {
        filename: "GY4_K052-3_No Filter_60S_Bin2_UTC20250826_152211_-14.9C_.fit",
        region: "K052-3",
        system: "GY4",
        ra: "18.72069h",
        dec: "+15.0",
        utcTime: "2025-08-26 15:22:11",
        startTime: 66,
        duration: 30
    },
    {
        filename: "GY4_K052-3_No Filter_60S_Bin2_UTC20250826_153854_-15C_.fit",
        region: "K052-3",
        system: "GY4",
        ra: "18.72069h",
        dec: "+14.99",
        utcTime: "2025-08-26 15:38:54",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-3_No Filter_60S_Bin2_UTC20250826_155535_-14.9C_.fit",
        region: "K052-3",
        system: "GY4",
        ra: "18.72069h",
        dec: "+14.98",
        utcTime: "2025-08-26 15:55:35",
        startTime: 68,
        duration: 30
    },
    {
        filename: "GY4_K052-4_No Filter_60S_Bin2_UTC20250826_152405_-14.9C_.fit",
        region: "K052-4",
        system: "GY4",
        ra: "17.78421h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:24:05",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-4_No Filter_60S_Bin2_UTC20250826_154048_-15C_.fit",
        region: "K052-4",
        system: "GY4",
        ra: "17.78421h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:40:48",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-4_No Filter_60S_Bin2_UTC20250826_155729_-14.9C_.fit",
        region: "K052-4",
        system: "GY4",
        ra: "17.78421h",
        dec: "+20.98",
        utcTime: "2025-08-26 15:57:29",
        startTime: 68,
        duration: 34
    },
    {
        filename: "GY4_K052-5_No Filter_60S_Bin2_UTC20250826_152556_-15C_.fit",
        region: "K052-5",
        system: "GY4",
        ra: "18.20526h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:25:56",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-5_No Filter_60S_Bin2_UTC20250826_154237_-14.9C_.fit",
        region: "K052-5",
        system: "GY4",
        ra: "18.20526h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:42:37",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-5_No Filter_60S_Bin2_UTC20250826_155920_-15C_.fit",
        region: "K052-5",
        system: "GY4",
        ra: "18.20526h",
        dec: "+20.98",
        utcTime: "2025-08-26 15:59:20",
        startTime: 68,
        duration: 30
    },
    {
        filename: "GY4_K052-6_No Filter_60S_Bin2_UTC20250826_152746_-15C_.fit",
        region: "K052-6",
        system: "GY4",
        ra: "18.62632h",
        dec: "+21.0",
        utcTime: "2025-08-26 15:27:46",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-6_No Filter_60S_Bin2_UTC20250826_154427_-15C_.fit",
        region: "K052-6",
        system: "GY4",
        ra: "18.62632h",
        dec: "+20.99",
        utcTime: "2025-08-26 15:44:27",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-6_No Filter_60S_Bin2_UTC20250826_160109_-15C_.fit",
        region: "K052-6",
        system: "GY4",
        ra: "18.62632h",
        dec: "+20.98",
        utcTime: "2025-08-26 16:01:09",
        startTime: 68,
        duration: 30
    },
    {
        filename: "GY4_K052-7_No Filter_60S_Bin2_UTC20250826_152940_-14.9C_.fit",
        region: "K052-7",
        system: "GY4",
        ra: "17.87778h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:29:40",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-7_No Filter_60S_Bin2_UTC20250826_154621_-15C_.fit",
        region: "K052-7",
        system: "GY4",
        ra: "17.87778h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:46:21",
        startTime: 68,
        duration: 34
    },
    {
        filename: "GY4_K052-7_No Filter_60S_Bin2_UTC20250826_160304_-15C_.fit",
        region: "K052-7",
        system: "GY4",
        ra: "17.87778h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:03:04",
        startTime: 65,
        duration: 34
    },
    {
        filename: "GY4_K052-8_No Filter_60S_Bin2_UTC20250826_153129_-15C_.fit",
        region: "K052-8",
        system: "GY4",
        ra: "18.32222h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:31:29",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-14.9C_.fit",
        region: "K052-8",
        system: "GY4",
        ra: "18.32222h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: 68,
        duration: 30
    },
    {
        filename: "GY4_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-15C_.fit",
        region: "K052-8",
        system: "GY4",
        ra: "18.32222h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: 65,
        duration: 30
    },
    {
        filename: "GY4_K052-9_No Filter_60S_Bin2_UTC20250826_153320_-14.9C_.fit",
        region: "K052-9",
        system: "GY4",
        ra: "18.76667h",
        dec: "+27.0",
        utcTime: "2025-08-26 15:33:20",
        startTime: 67,
        duration: 30
    },
    {
        filename: "GY4_K052-9_No Filter_60S_Bin2_UTC20250826_155001_-15C_.fit",
        region: "K052-9",
        system: "GY4",
        ra: "18.76667h",
        dec: "+26.99",
        utcTime: "2025-08-26 15:50:01",
        startTime: 68,
        duration: 35
    },
    {
        filename: "GY4_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY4",
        ra: "18.76667h",
        dec: "+26.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: 66,
        duration: 30
    },
    {
        filename: "GY4_K074-1_No Filter_60S_Bin2_UTC20250826_160847_-14.9C_.fit",
        region: "K074-1",
        system: "GY4",
        ra: "19.70000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:08:47",
        startTime: 68,
        duration: 30
    },
    {
        filename: "GY4_K074-1_No Filter_60S_Bin2_UTC20250826_162529_-14.9C_.fit",
        region: "K074-1",
        system: "GY4",
        ra: "19.70000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:25:29",
        startTime: 70,
        duration: 30
    },
    {
        filename: "GY4_K074-1_No Filter_60S_Bin2_UTC20250826_164210_-15C_.fit",
        region: "K074-1",
        system: "GY4",
        ra: "19.70000h",
        dec: "-3.02",
        utcTime: "2025-08-26 16:42:10",
        startTime: 73,
        duration: 30
    },
    {
        filename: "GY4_K074-2_No Filter_60S_Bin2_UTC20250826_161038_-15C_.fit",
        region: "K074-2",
        system: "GY4",
        ra: "20.10000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:10:38",
        startTime: 69,
        duration: 34
    },
    {
        filename: "GY4_K074-2_No Filter_60S_Bin2_UTC20250826_162718_-14.9C_.fit",
        region: "K074-2",
        system: "GY4",
        ra: "20.10000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:27:18",
        startTime: 70,
        duration: 34
    },
    {
        filename: "GY4_K074-2_No Filter_60S_Bin2_UTC20250826_164359_-14.9C_.fit",
        region: "K074-2",
        system: "GY4",
        ra: "20.10000h",
        dec: "-3.02",
        utcTime: "2025-08-26 16:43:59",
        startTime: 73,
        duration: 30
    },
    {
        filename: "GY4_K074-3_No Filter_60S_Bin2_UTC20250826_161226_-14.9C_.fit",
        region: "K074-3",
        system: "GY4",
        ra: "20.50000h",
        dec: "-3.0",
        utcTime: "2025-08-26 16:12:26",
        startTime: 69,
        duration: 34
    },
    {
        filename: "GY4_K074-3_No Filter_60S_Bin2_UTC20250826_162907_-14.9C_.fit",
        region: "K074-3",
        system: "GY4",
        ra: "20.50000h",
        dec: "-3.01",
        utcTime: "2025-08-26 16:29:07",
        startTime: 70,
        duration: 30
    },
    {
        filename: "GY4_K074-4_No Filter_60S_Bin2_UTC20250826_161422_-14.9C_.fit",
        region: "K074-4",
        system: "GY4",
        ra: "19.70000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:14:22",
        startTime: 69,
        duration: 30
    },
    {
        filename: "GY4_K074-4_No Filter_60S_Bin2_UTC20250826_163102_-15C_.fit",
        region: "K074-4",
        system: "GY4",
        ra: "19.70000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:31:02",
        startTime: 74,
        duration: 30
    },
    {
        filename: "GY4_K074-5_No Filter_60S_Bin2_UTC20250826_161611_-15C_.fit",
        region: "K074-5",
        system: "GY4",
        ra: "20.10000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:16:11",
        startTime: 70,
        duration: 36
    },
    {
        filename: "GY4_K074-5_No Filter_60S_Bin2_UTC20250826_163251_-14.9C_.fit",
        region: "K074-5",
        system: "GY4",
        ra: "20.10000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:32:51",
        startTime: 74,
        duration: 36
    },
    {
        filename: "GY4_K074-6_No Filter_60S_Bin2_UTC20250826_161759_-15C_.fit",
        region: "K074-6",
        system: "GY4",
        ra: "20.50000h",
        dec: "+3.0",
        utcTime: "2025-08-26 16:17:59",
        startTime: 71,
        duration: 34
    },
    {
        filename: "GY4_K074-6_No Filter_60S_Bin2_UTC20250826_163442_-15C_.fit",
        region: "K074-6",
        system: "GY4",
        ra: "20.50000h",
        dec: "+2.99",
        utcTime: "2025-08-26 16:34:42",
        startTime: 74,
        duration: 35
    },
    {
        filename: "GY4_K074-7_No Filter_60S_Bin2_UTC20250826_161955_-15C_.fit",
        region: "K074-7",
        system: "GY4",
        ra: "19.70000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:19:55",
        startTime: 72,
        duration: 30
    },
    {
        filename: "GY4_K074-7_No Filter_60S_Bin2_UTC20250826_163637_-14.9C_.fit",
        region: "K074-7",
        system: "GY4",
        ra: "19.70000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:36:37",
        startTime: 74,
        duration: 30
    },
    {
        filename: "GY4_K074-8_No Filter_60S_Bin2_UTC20250826_162146_-14.9C_.fit",
        region: "K074-8",
        system: "GY4",
        ra: "20.10000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:21:46",
        startTime: 72,
        duration: 30
    },
    {
        filename: "GY4_K074-8_No Filter_60S_Bin2_UTC20250826_163825_-15C_.fit",
        region: "K074-8",
        system: "GY4",
        ra: "20.10000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:38:25",
        startTime: 76,
        duration: 30
    },
    {
        filename: "GY4_K074-9_No Filter_60S_Bin2_UTC20250826_162335_-15C_.fit",
        region: "K074-9",
        system: "GY4",
        ra: "20.50000h",
        dec: "+9.0",
        utcTime: "2025-08-26 16:23:35",
        startTime: 73,
        duration: 30
    },
    {
        filename: "GY4_K074-9_No Filter_60S_Bin2_UTC20250826_164014_-15C_.fit",
        region: "K074-9",
        system: "GY4",
        ra: "20.50000h",
        dec: "+8.99",
        utcTime: "2025-08-26 16:40:14",
        startTime: 76,
        duration: 30
    },
    {
        filename: "GY5_K052-1_No Filter_60S_Bin2_UTC20250826_151831_-15C_.fit",
        region: "K052-1",
        system: "GY5",
        ra: "17.69310h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:18:31",
        startTime: 0,
        duration: 30
    },
    {
        filename: "GY5_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-14.9C_.fit",
        region: "K052-1",
        system: "GY5",
        ra: "17.69310h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: 0,
        duration: 30
    },
    {
        filename: "GY5_K052-1_No Filter_60S_Bin2_UTC20250826_155156_-15C_.fit",
        region: "K052-1",
        system: "GY5",
        ra: "17.69310h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:51:56",
        startTime: 90,
        duration: 34
    },
    {
        filename: "GY5_K052-2_No Filter_60S_Bin2_UTC20250826_152022_-15C_.fit",
        region: "K052-2",
        system: "GY5",
        ra: "18.10690h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:20:22",
        startTime: 0,
        duration: 31
    },
    {
        filename: "GY5_K052-2_No Filter_60S_Bin2_UTC20250826_153703_-15C_.fit",
        region: "K052-2",
        system: "GY5",
        ra: "18.10690h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:37:03",
        startTime: 80,
        duration: 30
    },
    {
        filename: "GY5_K052-2_No Filter_60S_Bin2_UTC20250826_155345_-14.9C_.fit",
        region: "K052-2",
        system: "GY5",
        ra: "18.10690h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:53:45",
        startTime: 90,
        duration: 34
    },
    {
        filename: "GY5_K052-3_No Filter_60S_Bin2_UTC20250826_152212_-15C_.fit",
        region: "K052-3",
        system: "GY5",
        ra: "18.52069h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:22:12",
        startTime: 0,
        duration: 33
    },
    {
        filename: "GY5_K052-3_No Filter_60S_Bin2_UTC20250826_153854_-15C_.fit",
        region: "K052-3",
        system: "GY5",
        ra: "18.52069h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:38:54",
        startTime: 80,
        duration: 30
    },
    {
        filename: "GY5_K052-3_No Filter_60S_Bin2_UTC20250826_155534_-14.9C_.fit",
        region: "K052-3",
        system: "GY5",
        ra: "18.52069h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:55:34",
        startTime: 90,
        duration: 30
    },
    {
        filename: "GY5_K052-4_No Filter_60S_Bin2_UTC20250826_152406_-14.9C_.fit",
        region: "K052-4",
        system: "GY5",
        ra: "17.58421h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:24:06",
        startTime: 79,
        duration: 30
    },
    {
        filename: "GY5_K052-4_No Filter_60S_Bin2_UTC20250826_154048_-15C_.fit",
        region: "K052-4",
        system: "GY5",
        ra: "17.58421h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:40:48",
        startTime: 82,
        duration: 30
    },
    {
        filename: "GY5_K052-4_No Filter_60S_Bin2_UTC20250826_155730_-15C_.fit",
        region: "K052-4",
        system: "GY5",
        ra: "17.58421h",
        dec: "+19.98",
        utcTime: "2025-08-26 15:57:30",
        startTime: 90,
        duration: 30
    },
    {
        filename: "GY5_K052-5_No Filter_60S_Bin2_UTC20250826_152556_-14.9C_.fit",
        region: "K052-5",
        system: "GY5",
        ra: "18.00526h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:25:56",
        startTime: 0,
        duration: 35
    },
    {
        filename: "GY5_K052-5_No Filter_60S_Bin2_UTC20250826_154236_-14.9C_.fit",
        region: "K052-5",
        system: "GY5",
        ra: "18.00526h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:42:36",
        startTime: 0,
        duration: 33
    },
    {
        filename: "GY5_K052-5_No Filter_60S_Bin2_UTC20250826_155920_-14.9C_.fit",
        region: "K052-5",
        system: "GY5",
        ra: "18.00526h",
        dec: "+19.98",
        utcTime: "2025-08-26 15:59:20",
        startTime: 90,
        duration: 35
    },
    {
        filename: "GY5_K052-6_No Filter_60S_Bin2_UTC20250826_152745_-15C_.fit",
        region: "K052-6",
        system: "GY5",
        ra: "18.42632h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:27:45",
        startTime: 79,
        duration: 30
    },
    {
        filename: "GY5_K052-6_No Filter_60S_Bin2_UTC20250826_154427_-15C_.fit",
        region: "K052-6",
        system: "GY5",
        ra: "18.42632h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:44:27",
        startTime: 83,
        duration: 30
    },
    {
        filename: "GY5_K052-6_No Filter_60S_Bin2_UTC20250826_160109_-15C_.fit",
        region: "K052-6",
        system: "GY5",
        ra: "18.42632h",
        dec: "+19.98",
        utcTime: "2025-08-26 16:01:09",
        startTime: 90,
        duration: 34
    },
    {
        filename: "GY5_K052-7_No Filter_60S_Bin2_UTC20250826_152941_-15C_.fit",
        region: "K052-7",
        system: "GY5",
        ra: "17.67778h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:29:41",
        startTime: 0,
        duration: 30
    },
    {
        filename: "GY5_K052-7_No Filter_60S_Bin2_UTC20250826_154621_-15C_.fit",
        region: "K052-7",
        system: "GY5",
        ra: "17.67778h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:46:21",
        startTime: 0,
        duration: 33
    },
    {
        filename: "GY5_K052-7_No Filter_60S_Bin2_UTC20250826_160305_-15C_.fit",
        region: "K052-7",
        system: "GY5",
        ra: "17.67778h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:03:05",
        startTime: 90,
        duration: 31
    },
    {
        filename: "GY5_K052-8_No Filter_60S_Bin2_UTC20250826_153130_-15C_.fit",
        region: "K052-8",
        system: "GY5",
        ra: "18.12222h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:31:30",
        startTime: 0,
        duration: 35
    },
    {
        filename: "GY5_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-15C_.fit",
        region: "K052-8",
        system: "GY5",
        ra: "18.12222h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: 86,
        duration: 30
    },
    {
        filename: "GY5_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-15C_.fit",
        region: "K052-8",
        system: "GY5",
        ra: "18.12222h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: 79,
        duration: 35
    },
    {
        filename: "GY5_K052-9_No Filter_60S_Bin2_UTC20250826_153319_-14.9C_.fit",
        region: "K052-9",
        system: "GY5",
        ra: "18.56667h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:33:19",
        startTime: 90,
        duration: 30
    },
    {
        filename: "GY5_K052-9_No Filter_60S_Bin2_UTC20250826_155000_-15C_.fit",
        region: "K052-9",
        system: "GY5",
        ra: "18.56667h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:50:00",
        startTime: 79,
        duration: 35
    },
    {
        filename: "GY5_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY5",
        ra: "18.56667h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: 79,
        duration: 35
    },
    {
        filename: "GY5_K074-1_No Filter_60S_Bin2_UTC20250826_160847_-15C_.fit",
        region: "K074-1",
        system: "GY5",
        ra: "19.50000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:08:47",
        startTime: 90,
        duration: 39
    },
    {
        filename: "GY5_K074-1_No Filter_60S_Bin2_UTC20250826_162529_-15C_.fit",
        region: "K074-1",
        system: "GY5",
        ra: "19.50000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:25:29",
        startTime: 90,
        duration: 30
    },
    {
        filename: "GY5_K074-1_No Filter_60S_Bin2_UTC20250826_164211_-15C_.fit",
        region: "K074-1",
        system: "GY5",
        ra: "19.50000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:42:11",
        startTime: 93,
        duration: 30
    },
    {
        filename: "GY5_K074-2_No Filter_60S_Bin2_UTC20250826_161038_-14.9C_.fit",
        region: "K074-2",
        system: "GY5",
        ra: "19.90000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:10:38",
        startTime: 90,
        duration: 35
    },
    {
        filename: "GY5_K074-2_No Filter_60S_Bin2_UTC20250826_162718_-14.9C_.fit",
        region: "K074-2",
        system: "GY5",
        ra: "19.90000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:27:18",
        startTime: 90,
        duration: 35
    },
    {
        filename: "GY5_K074-2_No Filter_60S_Bin2_UTC20250826_164359_-14.9C_.fit",
        region: "K074-2",
        system: "GY5",
        ra: "19.90000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:43:59",
        startTime: 93,
        duration: 36
    },
    {
        filename: "GY5_K074-3_No Filter_60S_Bin2_UTC20250826_161226_-15C_.fit",
        region: "K074-3",
        system: "GY5",
        ra: "20.30000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:12:26",
        startTime: 90,
        duration: 30
    },
    {
        filename: "GY5_K074-3_No Filter_60S_Bin2_UTC20250826_162909_-14.9C_.fit",
        region: "K074-3",
        system: "GY5",
        ra: "20.30000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:29:09",
        startTime: 90,
        duration: 30
    },
    {
        filename: "GY5_K074-3_No Filter_60S_Bin2_UTC20250826_164550_-14.9C_.fit",
        region: "K074-3",
        system: "GY5",
        ra: "20.30000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:45:50",
        startTime: 93,
        duration: 30
    },
    {
        filename: "GY5_K074-4_No Filter_60S_Bin2_UTC20250826_161420_-15C_.fit",
        region: "K074-4",
        system: "GY5",
        ra: "19.50000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:14:20",
        startTime: 90,
        duration: 30
    },
    {
        filename: "GY5_K074-4_No Filter_60S_Bin2_UTC20250826_163103_-15C_.fit",
        region: "K074-4",
        system: "GY5",
        ra: "19.50000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:31:03",
        startTime: 92,
        duration: 30
    },
    {
        filename: "GY5_K074-4_No Filter_60S_Bin2_UTC20250826_164744_-14.9C_.fit",
        region: "K074-4",
        system: "GY5",
        ra: "19.50000h",
        dec: "+1.98",
        utcTime: "2025-08-26 16:47:44",
        startTime: 93,
        duration: 36
    },
    {
        filename: "GY5_K074-5_No Filter_60S_Bin2_UTC20250826_161611_-15C_.fit",
        region: "K074-5",
        system: "GY5",
        ra: "19.90000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:16:11",
        startTime: 90,
        duration: 30
    },
    {
        filename: "GY5_K074-5_No Filter_60S_Bin2_UTC20250826_163252_-14.9C_.fit",
        region: "K074-5",
        system: "GY5",
        ra: "19.90000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:32:52",
        startTime: 93,
        duration: 30
    },
    {
        filename: "GY5_K074-6_No Filter_60S_Bin2_UTC20250826_161800_-15C_.fit",
        region: "K074-6",
        system: "GY5",
        ra: "20.30000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:18:00",
        startTime: 90,
        duration: 35
    },
    {
        filename: "GY5_K074-6_No Filter_60S_Bin2_UTC20250826_163441_-15C_.fit",
        region: "K074-6",
        system: "GY5",
        ra: "20.30000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:34:41",
        startTime: 93,
        duration: 34
    },
    {
        filename: "GY5_K074-7_No Filter_60S_Bin2_UTC20250826_161954_-15C_.fit",
        region: "K074-7",
        system: "GY5",
        ra: "19.50000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:19:54",
        startTime: 93,
        duration: 30
    },
    {
        filename: "GY5_K074-7_No Filter_60S_Bin2_UTC20250826_163637_-15C_.fit",
        region: "K074-7",
        system: "GY5",
        ra: "19.50000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:36:37",
        startTime: 93,
        duration: 30
    },
    {
        filename: "GY5_K074-8_No Filter_60S_Bin2_UTC20250826_162145_-15C_.fit",
        region: "K074-8",
        system: "GY5",
        ra: "19.90000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:21:45",
        startTime: 93,
        duration: 30
    },
    {
        filename: "GY5_K074-8_No Filter_60S_Bin2_UTC20250826_163826_-15C_.fit",
        region: "K074-8",
        system: "GY5",
        ra: "19.90000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:38:26",
        startTime: 93,
        duration: 30
    },
    {
        filename: "GY5_K074-9_No Filter_60S_Bin2_UTC20250826_162335_-14.9C_.fit",
        region: "K074-9",
        system: "GY5",
        ra: "20.30000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:23:35",
        startTime: 93,
        duration: 30
    },
    {
        filename: "GY5_K074-9_No Filter_60S_Bin2_UTC20250826_164015_-15C_.fit",
        region: "K074-9",
        system: "GY5",
        ra: "20.30000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:40:15",
        startTime: 93,
        duration: 36
    },
    {
        filename: "GY6_K052-1_No Filter_60S_Bin2_UTC20250826_151831_-15C_.fit",
        region: "K052-1",
        system: "GY6",
        ra: "17.89310h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:18:31",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-1_No Filter_60S_Bin2_UTC20250826_153514_-15C_.fit",
        region: "K052-1",
        system: "GY6",
        ra: "17.89310h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:35:14",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-1_No Filter_60S_Bin2_UTC20250826_155155_-15C_.fit",
        region: "K052-1",
        system: "GY6",
        ra: "17.89310h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:51:55",
        startTime: 94,
        duration: 33
    },
    {
        filename: "GY6_K052-2_No Filter_60S_Bin2_UTC20250826_152021_-15C_.fit",
        region: "K052-2",
        system: "GY6",
        ra: "18.30690h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:20:21",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-2_No Filter_60S_Bin2_UTC20250826_153703_-15C_.fit",
        region: "K052-2",
        system: "GY6",
        ra: "18.30690h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:37:03",
        startTime: 2,
        duration: 36
    },
    {
        filename: "GY6_K052-2_No Filter_60S_Bin2_UTC20250826_155345_-14.9C_.fit",
        region: "K052-2",
        system: "GY6",
        ra: "18.30690h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:53:45",
        startTime: 94,
        duration: 37
    },
    {
        filename: "GY6_K052-3_No Filter_60S_Bin2_UTC20250826_152212_-15C_.fit",
        region: "K052-3",
        system: "GY6",
        ra: "18.72069h",
        dec: "+14.0",
        utcTime: "2025-08-26 15:22:12",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-3_No Filter_60S_Bin2_UTC20250826_153853_-14.9C_.fit",
        region: "K052-3",
        system: "GY6",
        ra: "18.72069h",
        dec: "+13.99",
        utcTime: "2025-08-26 15:38:53",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-3_No Filter_60S_Bin2_UTC20250826_155534_-14.9C_.fit",
        region: "K052-3",
        system: "GY6",
        ra: "18.72069h",
        dec: "+13.98",
        utcTime: "2025-08-26 15:55:34",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-4_No Filter_60S_Bin2_UTC20250826_152406_-15C_.fit",
        region: "K052-4",
        system: "GY6",
        ra: "17.78421h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:24:06",
        startTime: 0,
        duration: 34
    },
    {
        filename: "GY6_K052-4_No Filter_60S_Bin2_UTC20250826_154049_-15C_.fit",
        region: "K052-4",
        system: "GY6",
        ra: "17.78421h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:40:49",
        startTime: 3,
        duration: 30
    },
    {
        filename: "GY6_K052-4_No Filter_60S_Bin2_UTC20250826_155730_-15C_.fit",
        region: "K052-4",
        system: "GY6",
        ra: "17.78421h",
        dec: "+19.98",
        utcTime: "2025-08-26 15:57:30",
        startTime: 94,
        duration: 34
    },
    {
        filename: "GY6_K052-5_No Filter_60S_Bin2_UTC20250826_152556_-15C_.fit",
        region: "K052-5",
        system: "GY6",
        ra: "18.20526h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:25:56",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-5_No Filter_60S_Bin2_UTC20250826_154237_-15C_.fit",
        region: "K052-5",
        system: "GY6",
        ra: "18.20526h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:42:37",
        startTime: 3,
        duration: 33
    },
    {
        filename: "GY6_K052-5_No Filter_60S_Bin2_UTC20250826_155920_-14.9C_.fit",
        region: "K052-5",
        system: "GY6",
        ra: "18.20526h",
        dec: "+19.98",
        utcTime: "2025-08-26 15:59:20",
        startTime: 94,
        duration: 34
    },
    {
        filename: "GY6_K052-6_No Filter_60S_Bin2_UTC20250826_152745_-15C_.fit",
        region: "K052-6",
        system: "GY6",
        ra: "18.62632h",
        dec: "+20.0",
        utcTime: "2025-08-26 15:27:45",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-6_No Filter_60S_Bin2_UTC20250826_154426_-15C_.fit",
        region: "K052-6",
        system: "GY6",
        ra: "18.62632h",
        dec: "+19.99",
        utcTime: "2025-08-26 15:44:26",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-6_No Filter_60S_Bin2_UTC20250826_160109_-15C_.fit",
        region: "K052-6",
        system: "GY6",
        ra: "18.62632h",
        dec: "+19.98",
        utcTime: "2025-08-26 16:01:09",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-7_No Filter_60S_Bin2_UTC20250826_152940_-15C_.fit",
        region: "K052-7",
        system: "GY6",
        ra: "17.87778h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:29:40",
        startTime: 1,
        duration: 30
    },
    {
        filename: "GY6_K052-7_No Filter_60S_Bin2_UTC20250826_154622_-14.9C_.fit",
        region: "K052-7",
        system: "GY6",
        ra: "17.87778h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:46:22",
        startTime: 3,
        duration: 30
    },
    {
        filename: "GY6_K052-7_No Filter_60S_Bin2_UTC20250826_160305_-15C_.fit",
        region: "K052-7",
        system: "GY6",
        ra: "17.87778h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:03:05",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-8_No Filter_60S_Bin2_UTC20250826_153129_-14.9C_.fit",
        region: "K052-8",
        system: "GY6",
        ra: "18.32222h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:31:29",
        startTime: 2,
        duration: 33
    },
    {
        filename: "GY6_K052-8_No Filter_60S_Bin2_UTC20250826_154810_-14.9C_.fit",
        region: "K052-8",
        system: "GY6",
        ra: "18.32222h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:48:10",
        startTime: 3,
        duration: 32
    },
    {
        filename: "GY6_K052-8_No Filter_60S_Bin2_UTC20250826_160453_-15C_.fit",
        region: "K052-8",
        system: "GY6",
        ra: "18.32222h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:04:53",
        startTime: 94,
        duration: 34
    },
    {
        filename: "GY6_K052-9_No Filter_60S_Bin2_UTC20250826_153320_-15C_.fit",
        region: "K052-9",
        system: "GY6",
        ra: "18.76667h",
        dec: "+26.0",
        utcTime: "2025-08-26 15:33:20",
        startTime: 2,
        duration: 36
    },
    {
        filename: "GY6_K052-9_No Filter_60S_Bin2_UTC20250826_155001_-15C_.fit",
        region: "K052-9",
        system: "GY6",
        ra: "18.76667h",
        dec: "+25.99",
        utcTime: "2025-08-26 15:50:01",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K052-9_No Filter_60S_Bin2_UTC20250826_160642_-14.9C_.fit",
        region: "K052-9",
        system: "GY6",
        ra: "18.76667h",
        dec: "+25.98",
        utcTime: "2025-08-26 16:06:42",
        startTime: 94,
        duration: 36
    },
    {
        filename: "GY6_K074-1_No Filter_60S_Bin2_UTC20250826_160848_-15C_.fit",
        region: "K074-1",
        system: "GY6",
        ra: "19.70000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:08:48",
        startTime: 94,
        duration: 36
    },
    {
        filename: "GY6_K074-1_No Filter_60S_Bin2_UTC20250826_162530_-14.9C_.fit",
        region: "K074-1",
        system: "GY6",
        ra: "19.70000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:25:30",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K074-1_No Filter_60S_Bin2_UTC20250826_164211_-15C_.fit",
        region: "K074-1",
        system: "GY6",
        ra: "19.70000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:42:11",
        startTime: 95,
        duration: 34
    },
    {
        filename: "GY6_K074-2_No Filter_60S_Bin2_UTC20250826_161037_-14.9C_.fit",
        region: "K074-2",
        system: "GY6",
        ra: "20.10000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:10:37",
        startTime: 94,
        duration: 32
    },
    {
        filename: "GY6_K074-2_No Filter_60S_Bin2_UTC20250826_162718_-14.9C_.fit",
        region: "K074-2",
        system: "GY6",
        ra: "20.10000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:27:18",
        startTime: 94,
        duration: 34
    },
    {
        filename: "GY6_K074-2_No Filter_60S_Bin2_UTC20250826_164359_-15C_.fit",
        region: "K074-2",
        system: "GY6",
        ra: "20.10000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:43:59",
        startTime: 95,
        duration: 34
    },
    {
        filename: "GY6_K074-3_No Filter_60S_Bin2_UTC20250826_161226_-15C_.fit",
        region: "K074-3",
        system: "GY6",
        ra: "20.50000h",
        dec: "-4.0",
        utcTime: "2025-08-26 16:12:26",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K074-3_No Filter_60S_Bin2_UTC20250826_162909_-14.9C_.fit",
        region: "K074-3",
        system: "GY6",
        ra: "20.50000h",
        dec: "-4.01",
        utcTime: "2025-08-26 16:29:09",
        startTime: 94,
        duration: 33
    },
    {
        filename: "GY6_K074-3_No Filter_60S_Bin2_UTC20250826_164550_-14.9C_.fit",
        region: "K074-3",
        system: "GY6",
        ra: "20.50000h",
        dec: "-4.02",
        utcTime: "2025-08-26 16:45:50",
        startTime: 95,
        duration: 30
    },
    {
        filename: "GY6_K074-4_No Filter_60S_Bin2_UTC20250826_161421_-14.9C_.fit",
        region: "K074-4",
        system: "GY6",
        ra: "19.70000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:14:21",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K074-4_No Filter_60S_Bin2_UTC20250826_163103_-15C_.fit",
        region: "K074-4",
        system: "GY6",
        ra: "19.70000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:31:03",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K074-4_No Filter_60S_Bin2_UTC20250826_164744_-14.9C_.fit",
        region: "K074-4",
        system: "GY6",
        ra: "19.70000h",
        dec: "+1.98",
        utcTime: "2025-08-26 16:47:44",
        startTime: 95,
        duration: 34
    },
    {
        filename: "GY6_K074-5_No Filter_60S_Bin2_UTC20250826_161612_-14.9C_.fit",
        region: "K074-5",
        system: "GY6",
        ra: "20.10000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:16:12",
        startTime: 94,
        duration: 35
    },
    {
        filename: "GY6_K074-5_No Filter_60S_Bin2_UTC20250826_163251_-15C_.fit",
        region: "K074-5",
        system: "GY6",
        ra: "20.10000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:32:51",
        startTime: 94,
        duration: 34
    },
    {
        filename: "GY6_K074-5_No Filter_60S_Bin2_UTC20250826_164933_-14.9C_.fit",
        region: "K074-5",
        system: "GY6",
        ra: "20.10000h",
        dec: "+1.98",
        utcTime: "2025-08-26 16:49:33",
        startTime: 95,
        duration: 33
    },
    {
        filename: "GY6_K074-6_No Filter_60S_Bin2_UTC20250826_161800_-15C_.fit",
        region: "K074-6",
        system: "GY6",
        ra: "20.50000h",
        dec: "+2.0",
        utcTime: "2025-08-26 16:18:00",
        startTime: 94,
        duration: 32
    },
    {
        filename: "GY6_K074-6_No Filter_60S_Bin2_UTC20250826_163442_-15C_.fit",
        region: "K074-6",
        system: "GY6",
        ra: "20.50000h",
        dec: "+1.99",
        utcTime: "2025-08-26 16:34:42",
        startTime: 94,
        duration: 31
    },
    {
        filename: "GY6_K074-7_No Filter_60S_Bin2_UTC20250826_161954_-14.9C_.fit",
        region: "K074-7",
        system: "GY6",
        ra: "19.70000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:19:54",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K074-7_No Filter_60S_Bin2_UTC20250826_163636_-14.9C_.fit",
        region: "K074-7",
        system: "GY6",
        ra: "19.70000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:36:36",
        startTime: 96,
        duration: 30
    },
    {
        filename: "GY6_K074-8_No Filter_60S_Bin2_UTC20250826_162145_-15C_.fit",
        region: "K074-8",
        system: "GY6",
        ra: "20.10000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:21:45",
        startTime: 94,
        duration: 30
    },
    {
        filename: "GY6_K074-8_No Filter_60S_Bin2_UTC20250826_163826_-15C_.fit",
        region: "K074-8",
        system: "GY6",
        ra: "20.10000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:38:26",
        startTime: 96,
        duration: 30
    },
    {
        filename: "GY6_K074-9_No Filter_60S_Bin2_UTC20250826_162336_-15C_.fit",
        region: "K074-9",
        system: "GY6",
        ra: "20.50000h",
        dec: "+8.0",
        utcTime: "2025-08-26 16:23:36",
        startTime: 95,
        duration: 34
    },
    {
        filename: "GY6_K074-9_No Filter_60S_Bin2_UTC20250826_164015_-15C_.fit",
        region: "K074-9",
        system: "GY6",
        ra: "20.50000h",
        dec: "+7.99",
        utcTime: "2025-08-26 16:40:15",
        startTime: 96,
        duration: 34
    }
];

// 获取当前状态
function getStatus(entry, currentTime) {
    if (currentTime < entry.startTime) {
        return "idle";
    } else if (currentTime < entry.startTime + 5) {
        return "waiting";
    } else if (currentTime < entry.startTime + entry.duration - 5) {
        return "processing";
    } else if (currentTime < entry.startTime + entry.duration) {
        return "completed";
    } else {
        return "idle";
    }
}

// 获取进度百分比
function getProgress(entry, currentTime) {
    if (currentTime <= entry.startTime + 5) return 0;
    if (currentTime >= entry.startTime + entry.duration - 5) return 100;

    var processTime = currentTime - entry.startTime - 5;
    var totalProcessTime = entry.duration - 10;
    return Math.floor((processTime / totalProcessTime) * 100);
}

// 获取状态文本
function getStatusText(entry, status, progress) {
    var baseText = entry.region + " [" + entry.system + "]";
    switch(status) {
        case "waiting": return baseText + " 准备";
        case "processing": return baseText + " " + progress + "%";
        case "completed": return baseText + " 完成";
        case "error": return baseText + " 错误";
        default: return baseText;
    }
}

core.output("开始显示FIT文件处理状态");
core.output("数据来源：日志文件分析");
core.output("文件数量：301个");
core.output("总时长：131秒（131分钟实际时间）");
core.output("时间尺度：0.3秒 = 1分钟");

// 存储上一次的状态，用于优化更新
var previousStates = {};
var previousStats = "";

// 主显示循环
for (var currentTime = 0; currentTime < 131; currentTime++) {
    // 计算时间和进度信息
    var hours = Math.floor(currentTime / 60);
    var minutes = currentTime % 60;
    var timeDisplay = "观测时间: " + hours + ":" + String(minutes).padStart(2, '0');

    var remainingTime = 131 - currentTime;
    var remainingHours = Math.floor(remainingTime / 60);
    var remainingMinutes = remainingTime % 60;
    var countdownText = "倒计时: " + remainingHours + ":" + String(remainingMinutes).padStart(2, '0');

    var overallProgress = Math.floor((currentTime / 131) * 100);
    var progressText = "总体进度: " + overallProgress + "% (" + currentTime + "/" + 131 + "秒)";

    // 统计状态
    var stats = {idle: 0, waiting: 0, processing: 0, completed: 0};
    var activeCount = 0;
    var currentStates = {};

    // 处理每个FIT文件
    for (var i = 0; i < fitTimeline.length; i++) {
        var entry = fitTimeline[i];
        var status = getStatus(entry, currentTime);
        var progress = getProgress(entry, currentTime);
        var labelName = entry.system + "_" + entry.region + "_" + i;

        stats[status]++;
        currentStates[labelName] = {status: status, progress: progress};

        // 检查状态是否发生变化
        var needsUpdate = false;
        if (!previousStates[labelName]) {
            needsUpdate = true;
        } else {
            var prevState = previousStates[labelName];
            if (prevState.status !== status || prevState.progress !== progress) {
                needsUpdate = true;
            }
        }

        // 只在状态变化时更新标签
        if (needsUpdate) {
            // 删除旧标签
            LabelMgr.deleteLabel(labelName);

            // 如果不是空闲状态，创建新标签
            if (status !== "idle") {
                var color = colors[status];
                var text = getStatusText(entry, status, progress);

                LabelMgr.labelEquatorial(
                    labelName,
                    entry.ra,
                    entry.dec,
                    true,
                    12,
                    color
                );
            }
        }

        if (status !== "idle") {
            activeCount++;
        }
    }

    // 计算统计和阶段进度信息
    var currentStatsText = "状态 - 等待:" + stats.waiting +
                          " 处理:" + stats.processing +
                          " 完成:" + stats.completed +
                          " 活跃:" + activeCount;

    var totalFiles = fitTimeline.length;
    var completionProgress = Math.floor((stats.completed / totalFiles) * 100);
    var processingProgress = Math.floor(((stats.completed + stats.processing) / totalFiles) * 100);
    var phaseProgressText = "阶段进度 - 完成:" + completionProgress + "% 进行中:" + processingProgress + "%";

    // 输出所有信息到控制台（每10秒输出一次，避免刷屏）
    if (currentTime % 10 === 0 || currentTime === 0) {
        core.output("=== 动态处理状态 (第" + currentTime + "秒) ===");
        core.output(timeDisplay);
        core.output(countdownText);
        core.output(progressText);
        core.output(currentStatsText);
        core.output(phaseProgressText);
        core.output("活跃天区数量: " + activeCount + "/" + totalFiles);
    }

    // 保存当前状态用于下次比较
    previousStates = currentStates;

    // 等待0.3秒
    core.wait(0.3);
}

core.output("FIT文件处理状态显示完成");
core.output("所有文件处理完毕");
