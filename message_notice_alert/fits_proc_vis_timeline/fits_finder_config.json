{"search_directories": ["E:/kats_process", "D:/KATS_DATA", "D:/kats"], "file_patterns": [{"type": "glob", "pattern": "*.fit", "case_sensitive": false, "description": "所有.fit文件"}], "options": {"recursive_search": true, "max_results": 500000, "exclude_patterns": ["*.tmp", "*.bak", "*.log"], "file_size_limits": {"min_size_mb": 0, "max_size_mb": 1000}, "path_filters": [{"type": "contains", "pattern": "/temp/", "case_sensitive": false, "description": "过滤临时目录"}, {"type": "contains", "pattern": "/backup/", "case_sensitive": false, "description": "过滤备份目录"}], "path_includes": [{"type": "regex", "pattern": "\\d{8}/K\\d{3}/GY\\d_K\\d{3}-.*?\\.fit$", "case_sensitive": false, "description": "只搜索.fit文件"}], "output_files": {"timeline_json": "vis_examples/examples/timeline/fits_data.json", "search_results": "fits_search_results.txt", "description": "输出文件路径配置"}}}