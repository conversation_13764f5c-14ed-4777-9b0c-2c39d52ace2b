<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Time zone</title>

  <style type="text/css">
    body, html {
      font-family: sans-serif;
      max-width: 800px;
    }
  </style>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>
<body>

<h1>Time zone</h1>

<p>
  The following demo shows how to display items in local time (default), in UTC, or for a specific time zone offset. By configuring your own <code>moment</code> constructor, you can display items in the time zone that you want. All timelines have the same start and end date.
</p>

<h2>Local time</h2>
<div id="local"></div>

<h2>UTC</h2>
<div id="utc"></div>

<h2>UTC +08:00</h2>
<div id="plus8"></div>


<script type="text/javascript">
  // Create a DataSet (allows two way data-binding)
  var today = vis.moment(vis.moment.utc().format('YYYY-MM-DDT00:00:00.000Z'));
  var start = today.clone();
  var end = today.clone().add(2, 'day');
  var customTime = today.clone().add(28, 'hour');

  var items = new vis.DataSet([
    {id: 1, content: 'item 1', start: today.clone().add(8, 'hour')},
    {id: 2, content: 'item 2', start: today.clone().add(16, 'hour')},
    {id: 3, content: 'item 3', start: today.clone().add(32, 'hour')}
  ]);

  // Create a timeline displaying in local time (default)
  var timelineLocal = new vis.Timeline(document.getElementById('local'), items, {
    editable: true,
    start: start,
    end: end
  });
  timelineLocal.addCustomTime(customTime);

  // Create a timeline displaying in UTC
  var timelineUTC = new vis.Timeline(document.getElementById('utc'), items, {
    editable: true,
    start: start,
    end: end,
    moment: function (date) {
      return vis.moment(date).utc();
    }
  });
  timelineUTC.addCustomTime(customTime);

  // Create a timeline displaying in UTC +08:00
  var timelinePlus8 = new vis.Timeline(document.getElementById('plus8'), items, {
    editable: true,
    start: start,
    end: end,
    moment: function (date) {
      return vis.moment(date).utcOffset('+08:00');
    }
  });
  timelinePlus8.addCustomTime(customTime);
</script>

</body>
</html>