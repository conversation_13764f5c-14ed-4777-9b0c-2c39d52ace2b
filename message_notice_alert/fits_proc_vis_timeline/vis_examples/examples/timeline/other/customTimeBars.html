<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Show current and custom time bars</title>

  <style type="text/css">
    body, html {
      font-family: sans-serif;
      font-size: 11pt;
    }
  </style>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>
<body>

<p>
  The Timeline has functions to add multiple custom time bars which can be dragged by the user.
</p>
<p>
  <input type="button" id="add" value="Add custom vertical bar">
  <input type="text" id="barId" placeholder="custom bar ID">
</p>
<p>
  <input type="button" id="remove" value="Remove custom vertical bar">
  <input type="text" id="barIndex" value="t1" placeholder="custom bar ID">
</p>
<p>
  <code><strong>timechange</strong></code> event, index: <span id="timechangeBar"></span>, time: <span id="timechangeEvent"></span>
</p>
<p>
  <code><strong>timechanged</strong></code> event, index: <span id="timechangedBar"></span>, time: <span id="timechangedEvent"></span>
</p><br>

<div id="visualization"></div>

<script type="text/javascript">
  var container = document.getElementById('visualization');
  var items = new vis.DataSet();
  var customDate = new Date();
  var options = {
    showCurrentTime: true,
    start: new Date(Date.now() - 1000 * 60 * 60 * 24),
    end: new Date(Date.now() + 1000 * 60 * 60 * 24 * 6)
  };
  var timeline = new vis.Timeline(container, items, options);

  // Set first time bar
  customDate = new Date(customDate.getFullYear(), customDate.getMonth(), customDate.getDate() + 1);
  timeline.addCustomTime(customDate, 't1');

  document.getElementById('add').onclick = function () {
    try {
      customDate = new Date(customDate.getFullYear(), customDate.getMonth(), customDate.getDate() + 1);
      var barId = document.getElementById('barId').value || undefined;
      timeline.addCustomTime(customDate, barId);
      document.getElementById('barId').value = '';
    }
    catch (err) {
      console.log(err);
      alert(err);
    }
  };

  document.getElementById('remove').onclick = function () {
    try {
      timeline.removeCustomTime(document.getElementById('barIndex').value);
      document.getElementById('barIndex').value = '';
    }
    catch (err) {
      console.log(err);
      alert(err);
    }
  };

  timeline.on('timechange', function (properties) {
    document.getElementById('timechangeBar').innerHTML = properties.id;
    document.getElementById('timechangeEvent').innerHTML = properties.time;
  });
  timeline.on('timechanged', function (properties) {
    document.getElementById('timechangedBar').innerHTML = properties.id;
    document.getElementById('timechangedEvent').innerHTML = properties.time;
  });

</script>
</body>
</html>