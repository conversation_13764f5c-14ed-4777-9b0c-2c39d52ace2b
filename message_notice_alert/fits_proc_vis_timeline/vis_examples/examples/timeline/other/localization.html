<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Localization</title>

  <style type="text/css">
    body, html, select {
      font-family: sans-serif;
      font-size: 11pt;
    }
  </style>

  <script src="http://cdnjs.cloudflare.com/ajax/libs/moment.js/2.8.1/moment-with-locales.min.js"></script>
  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>
<body>
<p>
  To localize the Timeline, one has to load a version of moment.js including locales. To set a locale, specify option <code>{locale: STRING}</code>.
</p>

<p>
  <label for="locale">Select a locale:</label>
  <select id="locale">
    <option value="en" selected>en</option>
    <option value="nl">nl</option>
  </select>
</p>

<div id="visualization"></div>

<script type="text/javascript">
  var DAY = 24 * 60 * 60 * 1000;

  // DOM element where the Timeline will be attached
  var container = document.getElementById('visualization');

  // Create a DataSet (allows two way data-binding)
  var items = new vis.DataSet([
    {id: 1, content: 'item 1', start: new Date(new Date().valueOf() - DAY)},
    {id: 2, content: 'item 2', start: new Date(new Date().valueOf() + 2 * DAY)}
  ]);

  // Configuration for the Timeline
  var options = {
    showCurrentTime: true
  };

  // Create a Timeline
  var timeline = new vis.Timeline(container, items, options);
  timeline.addCustomTime(new Date());

  timeline.setCustomTime(new Date(new Date().valueOf() + DAY));

  // update the locale when changing the select box value
  var select = document.getElementById('locale');
  select.onchange = function () {
    timeline.setOptions({
      locale: this.value
    });
  };
  select.onchange();
</script>
</body>
</html>