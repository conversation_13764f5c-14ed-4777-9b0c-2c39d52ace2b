<html>
<head>
  <title>Timeline | A lot of grouped data</title>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />

  <style type="text/css">
    body {
      color: #4D4D4D;
      font: 10pt arial;
    }
  </style>
  <script src="../../googleAnalytics.js"></script>
</head>

<body onresize="/*timeline.checkResize();*/">
<h1>Timeline grouping performance</h1>

<p>
  Choose a number of items:
  <a href="?count=100">100</a>,
  <a href="?count=1000">1000</a>,
  <a href="?count=10000">10000</a>,
  <a href="?count=100000">100000</a>
<p>
<p>
  Current number of items: <span id='count'>100</span>
</p>

<div id="mytimeline"></div>

<script>
  /**
   * Get URL parameter
   * http://www.netlobo.com/url_query_string_javascript.html
   */
  function gup( name ) {
    name = name.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");
    var regexS = "[\\?&]"+name+"=([^&#]*)";
    var regex = new RegExp( regexS );
    var results = regex.exec( window.location.href );
    if( results == null )
      return "";
    else
      return results[1];
  }

  // get selected item count from url parameter
  var count = (Number(gup('count')) || 1000);

  // create groups
  var groups = new vis.DataSet([
    {id: 1, content: 'Truck&nbsp;1'},
    {id: 2, content: 'Truck&nbsp;2'},
    {id: 3, content: 'Truck&nbsp;3'},
    {id: 4, content: 'Truck&nbsp;4'}
  ]);

  // create items
  var items = new vis.DataSet();

  var order = 1;
  var truck = 1;
  for (var j = 0; j < 4; j++) {
    var date = new Date();
    for (var i = 0; i < count/4; i++) {
      date.setHours(date.getHours() +  4 * (Math.random() < 0.2));
      var start = new Date(date);

      date.setHours(date.getHours() + 2 + Math.floor(Math.random()*4));
      var end = new Date(date);

      items.add({
        id: order,
        group: truck,
        start: start,
        end: end,
        content: 'Order ' + order
      });

      order++;
    }
    truck++;
  }

  // specify options
  var options = {
    stack: false,
    start: new Date(),
    end: new Date(1000*60*60*24 + (new Date()).valueOf()),
    editable: true,
    margin: {
      item: 10, // minimal margin between items
      axis: 5   // minimal margin between items and the axis
    },
    orientation: 'top'
  };

  // create a Timeline
  var container = document.getElementById('mytimeline');
  timeline = new vis.Timeline(container, null, options);
  timeline.setGroups(groups);
  timeline.setItems(items);

  document.getElementById('count').innerHTML = count;
</script>

</body>
</html>
