<!DOCTYPE HTML>
<html>
<head>
    <title>Timeline | Hiding periods</title>

    <style type="text/css">
        body, html {
            font-family: sans-serif;
        }
    </style>

    <script src="../../../dist/vis.js"></script>
    <link href="../../../dist/vis.css" rel="stylesheet" type="text/css"/>
    <script src="../../googleAnalytics.js"></script>
</head>
<body>
<p>
    It's possible to hide (recurring) periods from the Timeline. The following example hides weekends and nights.
</p>
<div id="visualization"></div>
<script type="text/javascript">
    // DOM element where the Timeline will be attached
    var container = document.getElementById('visualization');

    // Create a DataSet (allows two way data-binding)
    var items = new vis.DataSet([
        {id: 1, content: 'item 1', start: '2014-04-19'},
        {id: 2, content: 'item 2', start: '2014-04-21'},
        {id: 3, content: 'item 3', start: '2014-04-18'},
        {id: 4, content: 'item 4', start: '2014-04-16', end: '2014-04-24'},
        {id: 5, content: 'item 5', start: '2014-04-26 12:00:00'},
        {id: 6, content: 'item 6', start: '2014-04-27', type: 'point'}
    ]);

    // Configuration for the Timeline
    var options = {
        hiddenDates: [
            {start: '2014-03-21 00:00:00', end: '2014-03-28 00:00:00'},
            {start: '2013-10-26 00:00:00', end: '2013-10-28 00:00:00', repeat: 'weekly'}, // daily weekly monthly yearly
            {start: '2013-03-29 20:00:00', end: '2013-03-30 09:00:00', repeat: 'daily'} // daily weekly monthly yearly
        ],
        start: '2014-04-17',
        end: '2014-05-01',
        height: '200px',
        editable: true
    };

    // Create a Timeline
    var timeline = new vis.Timeline(container, items, options);
    timeline.addCustomTime("2014-04-18 13:00:00");
</script>
</body>
</html>