<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Basic demo</title>

  <style type="text/css">
    body, html {
      font-family: sans-serif;
    }
  </style>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>
<body>
<p>
  In this example all items get an HTML attribute attached: each item gets an attribute <code>data-id</code>, and items 1 and 6 have an additional attribute <code>data-tooltip</code>.
</p>

<div id="visualization"></div>

<script type="text/javascript">
  // DOM element where the Timeline will be attached
  var container = document.getElementById('visualization');

  // Create a DataSet (allows two way data-binding)
  var items = new vis.DataSet([
    {id: 1, content: 'item 1', start: '2014-04-20', tooltip: 'This is item 1'},
    {id: 2, content: 'item 2', start: '2014-04-14'},
    {id: 3, content: 'item 3', start: '2014-04-18'},
    {id: 4, content: 'item 4', start: '2014-04-16', end: '2014-04-19'},
    {id: 5, content: 'item 5', start: '2014-04-25'},
    {id: 6, content: 'item 6', start: '2014-04-27', type: 'point', tooltip: 'This is item 6'}
  ]);

  // Configuration for the Timeline
  var options = {dataAttributes: ['tooltip', 'id']};

  // Create a Timeline
  var timeline = new vis.Timeline(container, items, options);
</script>
</body>
</html>