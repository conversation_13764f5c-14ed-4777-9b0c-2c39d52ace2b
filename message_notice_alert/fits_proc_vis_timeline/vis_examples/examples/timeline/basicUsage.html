<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | FITS Files Demo</title>

  <style type="text/css">
    body, html {
      font-family: sans-serif;
    }
  </style>

  <script src="dist/vis.js"></script>
  <link href="dist/vis.css" rel="stylesheet" type="text/css" />

</head>
<body>

<p>
  FITS文件时间线展示。数据来自fits_file_finder_ripgrep.py的输出。您可以移动和缩放时间线，选择项目查看详细信息。
</p>

<div id="visualization"></div>

<script type="text/javascript">
  // DOM element where the Timeline will be attached
  var container = document.getElementById('visualization');

  // Load data from external JSON file (using small test file to avoid browser limitations)
  fetch('fits_data.json')
    .then(response => response.json())
    .then(data => {
      // Create a DataSet (allows two way data-binding)
      var items = new vis.DataSet(data);

      // Configuration for the Timeline
      var options = {};

      // Create a Timeline
      var timeline = new vis.Timeline(container, items, options);
    })
    .catch(error => {
      console.error('Error loading data:', error);
      // Show error message if data file cannot be loaded
      container.innerHTML = '<p style="color: red;">Error: Could not load data file fits_data_small.json</p>';
    });
</script>
</body>
</html>