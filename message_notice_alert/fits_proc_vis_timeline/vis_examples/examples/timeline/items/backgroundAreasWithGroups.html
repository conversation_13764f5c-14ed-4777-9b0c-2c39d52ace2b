<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Background areas with groups</title>

  <style>
    body, html {
      font-family: arial, sans-serif;
      font-size: 11pt;
    }
  </style>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>
<body>

<p>This example demonstrates the item type "background" when using groups.</p>
<ul>
  <li>Background items having a group are displayed in that group</li>
  <li>Background items without a group are spread over the whole timeline</li>
  <li>Background items with a non-existing group are not displayed</li>
</ul>
<div id="visualization"></div>

<script>
  var items = new vis.DataSet([
    {id: 'A', content: 'Period A', start: '2014-01-16', end: '2014-01-22', type: 'background', group: 1},
    {id: 'B', content: 'Period B', start: '2014-01-23', end: '2014-01-26', type: 'background', group: 2},
    {id: 'C', content: 'Period C', start: '2014-01-27', end: '2014-02-03', type: 'background'}, // no group
    {id: 'D', content: 'Period D', start: '2014-01-14', end: '2014-01-20', type: 'background', group: 'non-existing'},
    {id: 1, content: 'item 1<br>start', start: '2014-01-30', group: 1},
    {id: 2, content: 'item 2', start: '2014-01-18', group: 1},
    {id: 3, content: 'item 3', start: '2014-01-21', group: 2},
    {id: 4, content: 'item 4', start: '2014-01-17', end: '2014-01-21', group: 2},
    {id: 5, content: 'item 5', start: '2014-01-28', type:'point', group: 2},
    {id: 6, content: 'item 6', start: '2014-01-25', group: 2}
  ]);

  var groups = new vis.DataSet([
    {id: 1, content: 'Group 1'},
    {id: 2, content: 'Group 2'}
  ]);

  var container = document.getElementById('visualization');
  var options = {
    start: '2014-01-10',
    end: '2014-02-10',
    editable: true
  };

  var timeline = new vis.Timeline(container, items, groups, options);

</script>
</body>
</html>