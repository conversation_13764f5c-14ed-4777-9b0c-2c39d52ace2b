<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Point items</title>

  <style type="text/css">
    body {
      font: 10pt arial;
    }
  </style>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>
<body>
<h1>World War II timeline</h1>
<p>Source: <a href="http://www.onwar.com/chrono/index.htm" target="_blank">http://www.onwar.com/chrono/index.htm</a></p>
<div id="mytimeline" style="background-color: #FAFAFA;"></div>

<div id="visualization"></div>

<script type="text/javascript">
  var container = document.getElementById('visualization');

  // note that months are zero-based in the JavaScript Date object
  var items = new vis.DataSet([
    {start: new Date(1939,8,1),  content: 'German Invasion of Poland'},
    {start: new Date(1940,4,10), content: 'Battle of France and the Low Countries'},
    {start: new Date(1940,7,13), content: 'Battle of Britain - RAF vs. Luftwaffe'},
    {start: new Date(1941,1,14), content: 'German Afrika Korps arrives in North Africa'},
    {start: new Date(1941,5,22), content: 'Third Reich Invades the USSR'},
    {start: new Date(1941,11,7), content: 'Japanese Attack Pearl Harbor'},
    {start: new Date(1942,5,4),  content: 'Battle of Midway in the Pacific'},
    {start: new Date(1942,10,8), content: 'Americans open Second Front in North Africa'},
    {start: new Date(1942,10,19),content: 'Battle of Stalingrad in Russia'},
    {start: new Date(1943,6,5),  content: 'Battle of Kursk - Last German Offensive on Eastern Front'},
    {start: new Date(1943,6,10), content: 'Anglo-American Landings in Sicily'},
    {start: new Date(1944,2,8),  content: 'Japanese Attack British India'},
    {start: new Date(1944,5,6),  content: 'D-Day - Allied Invasion of Normandy'},
    {start: new Date(1944,5,22), content: 'Destruction of Army Group Center in Byelorussia'},
    {start: new Date(1944,7,1),  content: 'The Warsaw Uprising in Occupied Poland'},
    {start: new Date(1944,9,20), content: 'American Liberation of the Philippines'},
    {start: new Date(1944,11,16),content: 'Battle of the Bulge in the Ardennes'},
    {start: new Date(1944,1,19), content: 'American Landings on Iwo Jima'},
    {start: new Date(1945,3,1),  content: 'US Invasion of Okinawa'},
    {start: new Date(1945,3,16), content: 'Battle of Berlin - End of the Third Reich'}
  ]);

  var options = {
    // Set global item type. Type can also be specified for items individually
    // Available types: 'box' (default), 'point', 'range', 'rangeoverflow'
    type: 'point',
    showMajorLabels: false
  };

  var timeline = new vis.Timeline(container, items, options);
</script>
</body>
</html>
