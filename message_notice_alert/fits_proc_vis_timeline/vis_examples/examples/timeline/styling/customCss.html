<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Custom styling</title>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />

  <style type="text/css">
    body {
      font-family:  purisa, 'comic sans', cursive;
    }

    .vis-timeline {
      border: 2px solid purple;
      font-family:  purisa, 'comic sans', cursive;
      font-size: 12pt;
      background: #ffecea;
    }

    .vis-item {
      border-color: #F991A3;
      background-color: pink;
      font-size: 15pt;
      color: purple;
      box-shadow: 5px 5px 20px rgba(128,128,128, 0.5);
    }

    .vis-item,
    .vis-item.vis-line {
      border-width: 3px;
    }

    .vis-item.vis-dot {
      border-width: 10px;
      border-radius: 10px;
    }

    .vis-item.vis-selected {
      border-color: green;
      background-color: lightgreen;
    }

    .vis-time-axis .vis-text {
      color: purple;
      padding-top: 10px;
      padding-left: 10px;
    }

    .vis-time-axis .vis-text.vis-major {
      font-weight: bold;
    }

    .vis-time-axis .vis-grid.vis-minor {
      border-width: 2px;
      border-color: pink;
    }

    .vis-time-axis .vis-grid.vis-major {
      border-width: 2px;
      border-color: #F991A3;
    }
  </style>

  <script src="../../googleAnalytics.js"></script>
</head>
<body>

<p>
  The style of the Timeline can be fully customized via CSS:
</p>
<div id="visualization"></div>

<script type="text/javascript">
  var container = document.getElementById('visualization');

  // note that months are zero-based in the JavaScript Date object
  var items = new vis.DataSet([
    {start: new Date(2010,7,23), content: '<div>Conversation</div><img src="../resources/img/community-users-icon.png" style="width:32px; height:32px;">'},
    {start: new Date(2010,7,23,23,0,0), content: '<div>Mail from boss</div><img src="../resources/img/mail-icon.png" style="width:32px; height:32px;">'},
    {start: new Date(2010,7,24,16,0,0), content: 'Report'},
    {start: new Date(2010,7,26), end: new Date(2010,8,2), content: 'Traject A'},
    {start: new Date(2010,7,28), content: '<div>Memo</div><img src="../resources/img/notes-edit-icon.png" style="width:48px; height:48px;">'},
    {start: new Date(2010,7,29), content: '<div>Phone call</div><img src="../resources/img/Hardware-Mobile-Phone-icon.png" style="width:32px; height:32px;">'},
    {start: new Date(2010,7,31), end: new Date(2010,8,3), content: 'Traject B'},
    {start: new Date(2010,8,4,12,0,0), content: '<div>Report</div><img src="../resources/img/attachment-icon.png" style="width:32px; height:32px;">'}
  ]);

  var options = {
    editable: true,
    margin: {
      item: 20,
      axis: 40
    }
  };

  var timeline = new vis.Timeline(container, items, options);
</script>
</body>
</html>