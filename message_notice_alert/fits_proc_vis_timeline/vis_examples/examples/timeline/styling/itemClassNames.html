<html>
<head>
  <title>Timeline | Item class names</title>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />

  <style type="text/css">
    body, input {
      font: 12pt verdana;
    }

    /* custom styles for individual items, load this after vis.css */

    .vis-item.green {
      background-color: greenyellow;
      border-color: green;
    }

    /* create a custom sized dot at the bottom of the red item */
    .vis-item.red {
      background-color: red;
      border-color: darkred;
      color: white;
      font-family: monospace;
      box-shadow: 0 0 10px gray;
    }
    .vis-item.vis-dot.red {
      border-radius: 10px;
      border-width: 10px;
    }
    .vis-item.vis-line.red {
      border-width: 5px;
    }
    .vis-item.vis-box.red {
      border-radius: 0;
      border-width: 2px;
      font-size: 24pt;
      font-weight: bold;
    }

    .vis-item.orange {
      background-color: gold;
      border-color: orange;
    }
    .vis-item.vis-selected.orange {
      /* custom colors for selected orange items */
      background-color: orange;
      border-color: orangered;
    }

    .vis-item.magenta {
      background-color: magenta;
      border-color: purple;
      color: white;
    }

    /* our custom classes overrule the styles for selected events,
       so lets define a new style for the selected events */
    .vis-item.vis-selected {
      background-color: white;
      border-color: black;
      color: black;
      box-shadow: 0 0 10px gray;
    }
  </style>

  <script src="../../googleAnalytics.js"></script>
</head>
<body>
<p>This page demonstrates the Timeline with custom css classes for individual items.</p>

<div id="mytimeline"></div>

<script type="text/javascript">
  // create data
  // note that months are zero-based in the JavaScript Date object
  var data = new vis.DataSet([
    {
      'start': new Date(2012,7,19),
      'content': 'default'
    },
    {
      'start': new Date(2012,7,23),
      'content': 'green',
      'className': 'green'
    },
    {
      'start': new Date(2012,7,29),
      'content': 'red',
      'className': 'red'
    },
    {
      'start': new Date(2012,7,27),
      'end': new Date(2012,8,1),
      'content': 'orange',
      'className': 'orange'
    },
    {
      'start': new Date(2012,8,2),
      'content': 'magenta',
      'className': 'magenta'
    }
  ]);

  // specify options
  var options = {
    editable: true
  };

  // create the timeline
  var container = document.getElementById('mytimeline');
  timeline = new vis.Timeline(container, data, options);

</script>
</body>
</html>
