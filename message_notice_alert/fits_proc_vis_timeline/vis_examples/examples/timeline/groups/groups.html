<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Group example</title>

  <style>
    body, html {
      font-family: arial, sans-serif;
      font-size: 11pt;
    }

    #visualization {
      box-sizing: border-box;
      width: 100%;
      height: 300px;
    }
  </style>

  <!-- note: moment.js must be loaded before vis.js, else vis.js uses its embedded version of moment.js -->
  <script src="http://cdnjs.cloudflare.com/ajax/libs/moment.js/2.8.4/moment.min.js"></script>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>
<body>
<p>
  This example demonstrate using groups. Note that a DataSet is used for both
  items and groups, allowing to dynamically add, update or remove both items
  and groups via the DataSet.
</p>
<div id="visualization"></div>

<script>
  var now = moment().minutes(0).seconds(0).milliseconds(0);
  var groupCount = 3;
  var itemCount = 20;

  // create a data set with groups
  var names = ['<PERSON>', '<PERSON><PERSON>', 'Lee', 'Grant'];
  var groups = new vis.DataSet();
  for (var g = 0; g < groupCount; g++) {
    groups.add({id: g, content: names[g]});
  }

  // create a dataset with items
  var items = new vis.DataSet();
  for (var i = 0; i < itemCount; i++) {
    var start = now.clone().add(Math.random() * 200, 'hours');
    var group = Math.floor(Math.random() * groupCount);
    items.add({
      id: i,
      group: group,
      content: 'item ' + i +
          ' <span style="color:#97B0F8;">(' + names[group] + ')</span>',
      start: start,
      type: 'box'
    });
  }

  // create visualization
  var container = document.getElementById('visualization');
  var options = {
    groupOrder: 'content'  // groupOrder can be a property name or a sorting function
  };

  var timeline = new vis.Timeline(container);
  timeline.setOptions(options);
  timeline.setGroups(groups);
  timeline.setItems(items);

</script>
</body>
</html>