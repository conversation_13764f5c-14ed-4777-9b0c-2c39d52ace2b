<!DOCTYPE HTML>
<html>
<head>
    <title>Timeline | Background areas</title>

    <style>
        body, html {
            font-family: arial, sans-serif;
            font-size: 11pt;
        }

        .vis-item.vis-background.negative {
            background-color: rgba(255, 0, 0, 0.2);
        }
        .vis-item.vis-background.positive {
            background-color: rgba(105, 255, 98, 0.20);
        }
        .vis-item.vis-background.marker {
            border-left: 2px solid green;
        }
    </style>

    <script src="../../../dist/vis.js"></script>
    <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
    <script src="../../googleAnalytics.js"></script>
</head>
<body>

<p>This example shows the workings of the subgroups. Subgroups do not use stacking, and only work when stacking is disabled.</p>

<div id="visualization"></div>

<script>
    // create a dataset with items
    // we specify the type of the fields `start` and `end` here to be strings
    // containing an ISO date. The fields will be outputted as ISO dates
    // automatically getting data from the DataSet via items.get().
    var items = new vis.DataSet({
        type: { start: 'ISODate', end: 'ISODate' }
    });
    var groups = new vis.DataSet([{
        id: 'bar', content:'bar', subgroupOrder: function (a,b) {return a.subgroupOrder - b.subgroupOrder;}
    },{
        id: 'foo', content:'foo', subgroupOrder: 'subgroupOrder' // this group has no subgroups but this would be the other method to do the sorting.
    }]);
    // add items to the DataSet
    items.add([
        {id: 'A',start: '2014-01-20', end: '2014-01-22', type: 'background', group:'foo'},
        {id: 'B',start: '2014-01-22', end: '2014-01-23', type: 'background', group:'foo', className: 'negative'},
        {id: 0, content: 'no subgroup', start: '2014-01-20', end: '2014-01-22',group:'foo'},

        {id: 'SG_1_1',start: '2014-01-25', end: '2014-01-27', type: 'background', group:'bar', subgroup:'sg_1', subgroupOrder:0},
        {id: 'SG_1_2', start: '2014-01-26', end: '2014-01-27', type: 'background', className: 'positive',group:'bar', subgroup:'sg_1', subgroupOrder:0},
        {id: 1, content: 'subgroup0', start: '2014-01-23 12:00:00', end: '2014-01-26 12:00:00',group:'bar', subgroup:'sg_1', subgroupOrder:0},
        {id: 'SG_2_1', start: '2014-01-27', end: '2014-01-29', type: 'background', group:'bar', subgroup:'sg_2', subgroupOrder:1},
        {id: 'SG_2_2', start: '2014-01-27', end: '2014-01-28', type: 'background', className: 'negative',group:'bar', subgroup:'sg_2', subgroupOrder:1},
        {id: 2, content: 'subgroup1', start: '2014-01-27', end: '2014-01-29',group:'bar', subgroup:'sg_2', subgroupOrder:1},

        {id: 'background', start: '2014-01-29', end: '2014-01-30', type: 'background', className: 'negative',group:'bar'},
        {id: 'background_all', start: '2014-01-31', end: '2014-02-02', type: 'background', className: 'positive'},
    ]);

    var container = document.getElementById('visualization');
    var options = {
        // orientation:'top'
        start: '2014-01-10',
        end: '2014-02-10',
        editable: true,
        stack: false
    };

    var timeline = new vis.Timeline(container, items, groups, options);

</script>
</body>
</html>