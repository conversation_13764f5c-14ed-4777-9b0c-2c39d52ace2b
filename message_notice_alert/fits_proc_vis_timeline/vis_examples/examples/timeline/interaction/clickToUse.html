<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Click to use</title>

  <style>
    body, html {
      font-family: arial, sans-serif;
      font-size: 11pt;
    }
    #main {
      width: 728px;
      margin: 0 auto;
    }
    .container {
      margin: 10px;
    }
  </style>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>
<body>
<div id="main">
  <h1>Timeline click to use</h1>
  <p>
    This example demonstrates how to use the <code>clickToUse</code> option: before you can scroll and drag in the timeline, you first have to click in the timeline to activate.
  </p>
</div>

<script>
  // create a dataset with items
  // we specify the type of the fields `start` and `end` here to be strings
  // containing an ISO date. The fields will be outputted as ISO dates
  // automatically getting data from the DataSet via items.get().
  var items = new vis.DataSet({
    type: { start: 'ISODate', end: 'ISODate' }
  });

  // add items to the DataSet
  items.add([
    {id: 1, content: 'item 1<br>start', start: '2014-01-23'},
    {id: 2, content: 'item 2', start: '2014-01-18'},
    {id: 3, content: 'item 3', start: '2014-01-21'},
    {id: 4, content: 'item 4', start: '2014-01-19', end: '2014-01-24'},
    {id: 5, content: 'item 5', start: '2014-01-28', type:'point'},
    {id: 6, content: 'item 6', start: '2014-01-26'}
  ]);

  function createTimeline(main) {
    var main = document.getElementById('main');
    var container = document.createElement('div');
    container.className = 'container';
    main.appendChild(container);

    var options = {
      editable: true,
      clickToUse: true
    };

    return new vis.Timeline(container, items, options);
  }

  var timelines = [];
  for (var i = 0; i < 10; i++) {
    timelines.push(createTimeline());
  }


</script>
</body>
</html>