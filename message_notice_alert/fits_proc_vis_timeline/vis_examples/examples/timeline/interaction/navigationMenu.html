<!DOCTYPE HTML>
<html>
<head>
    <title>Timeline | navigation menu</title>

    <style type="text/css">
        body, html, input {
            font-family: sans-serif;
            font-size: 12pt;
        }

        #visualization {
            position: relative;
        }

        .menu {
            position: absolute;
            top: 0;
            right: 0;
            margin: 10px;
            z-index: 9999;
        }
    </style>

    <script src="../../../dist/vis.js"></script>
    <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
    <script src="../../googleAnalytics.js"></script>
</head>
<body>

<p>
    Create your own navigation menu by creating an overlay with buttons to zoom and move.
</p>

<div id="visualization">
    <div class="menu">
        <input type="button" id="zoomIn" value="Zoom in"/>
        <input type="button" id="zoomOut" value="Zoom out"/>
        <input type="button" id="moveLeft" value="Move left"/>
        <input type="button" id="moveRight" value="Move right"/>
    </div>
</div>

<script type="text/javascript">
    // create a timeline with some data
    var container = document.getElementById('visualization');
    var items = new vis.DataSet([
        {id: 1, content: 'item 1', start: '2014-04-20'},
        {id: 2, content: 'item 2', start: '2014-04-14'},
        {id: 3, content: 'item 3', start: '2014-04-18'},
        {id: 4, content: 'item 4', start: '2014-04-16', end: '2014-04-19'},
        {id: 5, content: 'item 5', start: '2014-04-25'},
        {id: 6, content: 'item 6', start: '2014-04-27', type: 'point'}
    ]);
    var options = {};
    var timeline = new vis.Timeline(container, items, options);

    /**
     * Move the timeline a given percentage to left or right
     * @param {Number} percentage   For example 0.1 (left) or -0.1 (right)
     */
    function move (percentage) {
        var range = timeline.getWindow();
        var interval = range.end - range.start;

        timeline.setWindow({
            start: range.start.valueOf() - interval * percentage,
            end:   range.end.valueOf()   - interval * percentage
        });
    }

    /**
     * Zoom the timeline a given percentage in or out
     * @param {Number} percentage   For example 0.1 (zoom out) or -0.1 (zoom in)
     */
    function zoom (percentage) {
        var range = timeline.getWindow();
        var interval = range.end - range.start;

        timeline.setWindow({
            start: range.start.valueOf() - interval * percentage,
            end:   range.end.valueOf()   + interval * percentage
        });
    }

    // attach events to the navigation buttons
    document.getElementById('zoomIn').onclick    = function () { zoom(-0.2); };
    document.getElementById('zoomOut').onclick   = function () { zoom( 0.2); };
    document.getElementById('moveLeft').onclick  = function () { move( 0.2); };
    document.getElementById('moveRight').onclick = function () { move(-0.2); };

</script>
</body>
</html>