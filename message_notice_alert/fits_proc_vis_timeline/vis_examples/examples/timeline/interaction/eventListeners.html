<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Event listeners</title>

  <style type="text/css">
    body, html {
      font-family: sans-serif;
    }
  </style>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>
<body>
  <p>
    This example listens for events <code>select</code>, <code>rangechange</code>, and <code>rangechanged</code> of the Timeline, and listens for changes in the DataSet (<code>add</code>, <code>update</code>, or <code>remove</code> items).
  </p>
  <div id="visualization"></div>
  <p></p>
  <div id="log"></div>

  <script type="text/javascript">
    var items = new vis.DataSet([
      {id: 1, content: 'item 1', start: '2013-04-20'},
      {id: 2, content: 'item 2', start: '2013-04-14'},
      {id: 3, content: 'item 3', start: '2013-04-18'},
      {id: 4, content: 'item 4', start: '2013-04-16', end: '2013-04-19'},
      {id: 5, content: 'item 5', start: '2013-04-25'},
      {id: 6, content: 'item 6', start: '2013-04-27'}
    ]);

    var container = document.getElementById('visualization');
    var options = {
      editable: true
    };
    var timeline = new vis.Timeline(container, items, options);

    timeline.on('rangechange', function (properties) {
      logEvent('rangechange', properties);
    });
    timeline.on('rangechanged', function (properties) {
      logEvent('rangechanged', properties);
    });
    timeline.on('select', function (properties) {
      logEvent('select', properties);
    });

    items.on('*', function (event, properties) {
      logEvent(event, properties);
    });

    function logEvent(event, properties) {
      var log = document.getElementById('log');
      var msg = document.createElement('div');
      msg.innerHTML = 'event=' + JSON.stringify(event) + ', ' +
          'properties=' + JSON.stringify(properties);
      log.firstChild ? log.insertBefore(msg, log.firstChild) : log.appendChild(msg);
    }

  </script>
</body>
</html>