<html>
<head>
  <title>Timeline | Update data on event</title>

  <style type="text/css">
    body {
      font: 11pt verdana;
    }

    .vis.timeline .item.past {
      filter: alpha(opacity=50);
      opacity: 0.5;
    }
  </style>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>

<body>
<p style="width: 600px;">
  When the custom time bar is shown, the user can drag this bar to a specific
  time. The Timeline sends an event that the custom time is changed, after
  which the contents of the timeline can be changed according to the specified
  time in past or future.
</p>

<div id="customTime">&nbsp;</div>
<p></p>

<div id="mytimeline"></div>


<script>
  // create a data set
  var data = new vis.DataSet([
    {
      id: 1,
      start: new Date((new Date()).getTime() - 60 * 1000),
      end: new Date(),
      content: 'Dynamic event'
    }
  ]);

  // specify options
  var options = {
    showCurrentTime: true
  };

  // create a timeline
  var container = document.getElementById('mytimeline');
  timeline = new vis.Timeline(container, data, options);

  timeline.addCustomTime(new Date());

  // add event listener
  timeline.on('timechange', function (event) {
    document.getElementById("customTime").innerHTML = "Custom Time: " + event.time;

    var item = data.get(1);
    if (event.time > item.start) {
      item.end = new Date(event.time);
      var now = new Date();
      if (event.time < now) {
        item.content = "Dynamic event (past)";
        item.className = 'past';
      }
      else if (event.time > now) {
        item.content = "Dynamic event (future)";
        item.className = 'future';
      }
      else  {
        item.content = "Dynamic event (now)";
        item.className = 'now';
      }

      data.update(item);
    }
  });

  // set a custom range from -2 minute to +3 minutes current time
  var start = new Date((new Date()).getTime() - 2 * 60 * 1000);
  var end   = new Date((new Date()).getTime() + 3 * 60 * 1000);
  timeline.setWindow(start, end, {animation: false});

</script>

</body>
</html>
