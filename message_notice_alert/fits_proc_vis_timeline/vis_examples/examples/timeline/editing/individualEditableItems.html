<!DOCTYPE HTML>
<html>
<head>
  <title>Timeline | Individual editable items</title>

  <style>
    body, html {
      font-family: arial, sans-serif;
      font-size: 11pt;
    }

    div.vis-editable,
    div.vis-editable.vis-selected {
      /* custom styling for editable items... */
    }

    div.vis-readonly,
    div.vis-readonly.vis-selected {
      /* custom styling for readonly items... */
      background-color: #ff4500;
      border-color: red;
      color: white;
    }
  </style>

  <script src="../../../dist/vis.js"></script>
  <link href="../../../dist/vis.css" rel="stylesheet" type="text/css" />
  <script src="../../googleAnalytics.js"></script>
</head>
<body>

<p>Specify individual items to be editable or readonly.</p>

<div id="visualization"></div>

<script>
  // create a DataSet with items
  var items = new vis.DataSet([
    {id: 1, content: 'Editable', editable: true, start: '2010-08-23'},
    {id: 2, content: 'Editable', editable: true, start: '2010-08-23T23:00:00'},
    {id: 3, content: 'Read-only', editable: false, start: '2010-08-24T16:00:00'},
    {id: 4, content: 'Read-only', editable: false, start: '2010-08-26', end: '2010-09-02'},
    {id: 5, content: 'Editable', editable: true, start: '2010-08-28'},
    {id: 6, content: 'Read-only', editable: false, start: '2010-08-29'},
    {id: 7, content: 'Editable', editable: true, start: '2010-08-31', end: '2010-09-03'},
    {id: 8, content: 'Read-only', editable: false, start: '2010-09-04T12:00:00'}
  ]);

  var container = document.getElementById('visualization');
  var options = {
    editable: true   // default for all items
  };

  var timeline = new vis.Timeline(container, items, options);

</script>
</body>
</html>