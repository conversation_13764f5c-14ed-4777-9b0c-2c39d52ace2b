
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named fcntl - imported by subprocess (optional), psutil._compat (delayed, optional), astropy.utils.console (optional), xmlrpc.server (optional)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named pwd - imported by posixpath (delayed, conditional), subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), http.server (delayed, optional), webbrowser (delayed), psutil (optional), netrc (delayed, conditional), getpass (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional)
missing module named grp - imported by subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), fsspec.asyn (optional), IPython.utils.timing (optional)
missing module named urllib.FancyURLopener - imported by urllib (conditional, optional), pygments.lexers._sourcemod_builtins (conditional, optional)
missing module named urllib.urlretrieve - imported by urllib (conditional, optional), pygments.lexers._php_builtins (conditional, optional)
missing module named urllib.urlopen - imported by urllib (conditional, optional), pygments.lexers._lua_builtins (conditional, optional), pygments.lexers._postgres_builtins (conditional, optional)
missing module named urllib.getproxies_environment - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.proxy_bypass_environment - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.proxy_bypass - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.getproxies - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.urlencode - imported by urllib (conditional), requests.compat (conditional), ephem.cities (conditional)
missing module named urllib.unquote_plus - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.quote_plus - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.unquote - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.quote - imported by urllib (conditional), requests.compat (conditional)
missing module named org - imported by pickle (optional)
missing module named termios - imported by tty (top-level), psutil._compat (delayed, optional), getpass (optional), astropy.utils.console (delayed, optional), IPython.core.page (delayed, optional), prompt_toolkit.input.vt100 (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), requests.utils (delayed, conditional, optional), pkg_resources._vendor.appdirs (delayed, conditional), pygments.formatters.img (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pep517 - imported by importlib.metadata (delayed)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pyimod02_importers - imported by C:\Python\Python310\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Python\Python310\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _manylinux - imported by pkg_resources._vendor.packaging.tags (delayed, optional), packaging._manylinux (delayed, optional), setuptools._vendor.packaging.tags (delayed, optional)
missing module named startup - imported by pyreadline3.keysyms.common (conditional), pyreadline3.keysyms.keysyms (conditional)
missing module named sets - imported by pyreadline3.keysyms.common (optional), pytz.tzinfo (optional)
missing module named System - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.keysyms.ironpython_keysyms (top-level), pyreadline3.console.ironpython_console (top-level), pyreadline3.rlmain (conditional), IPython.utils._process_cli (top-level)
missing module named console - imported by pyreadline3.console.ansi (conditional)
missing module named clr - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.console.ironpython_console (top-level), IPython.utils._process_cli (top-level)
missing module named IronPythonConsole - imported by pyreadline3.console.ironpython_console (top-level)
missing module named __builtin__ - imported by debugpy.common.compat (conditional), pkg_resources._vendor.pyparsing (conditional), setuptools._vendor.pyparsing (conditional)
missing module named ordereddict - imported by pkg_resources._vendor.pyparsing (optional), setuptools._vendor.pyparsing (optional)
missing module named 'pkg_resources.extern.pyparsing' - imported by pkg_resources._vendor.packaging.markers (top-level), pkg_resources._vendor.packaging.requirements (top-level)
missing module named 'com.sun' - imported by pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named com - imported by pkg_resources._vendor.appdirs (delayed)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.appdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'setuptools.extern.pyparsing' - imported by setuptools._vendor.packaging.markers (top-level), setuptools._vendor.packaging.requirements (top-level)
missing module named 'setuptools.extern.more_itertools' - imported by setuptools.dist (top-level), setuptools.msvc (top-level)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools.config (top-level), setuptools.msvc (top-level)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.config (top-level)
missing module named setuptools.extern.ordered_set - imported by setuptools.extern (top-level), setuptools.dist (top-level)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools.dist (top-level), setuptools.command.egg_info (top-level)
missing module named astropy.coordinates.SkyCoord - imported by astropy.coordinates (delayed), astropy.io.ascii.mrt (delayed), astropy.coordinates.jparser (top-level), astropy.coordinates.spectral_coordinate (top-level), astropy.wcs.wcsapi.high_level_api (delayed, conditional), astropy.visualization.wcsaxes.core (top-level), astropy.visualization.wcsaxes.transforms (top-level), astropy.visualization.wcsaxes.wcsapi (top-level), astropy.visualization.wcsaxes.patches (top-level), astropy.wcs.wcsapi.fitswcs (delayed), astropy.wcs.utils (delayed), astropy.nddata.utils (top-level), C:\gitroot\python_tests\sources\run_get_fits_by_ra_dec.py (top-level), astropy.coordinates.orbital_elements (top-level), astropy.coordinates.tests.accuracy.generate_spectralcoord_ref (conditional), astropy.coordinates.tests.accuracy.test_altaz_icrs (top-level), astropy.coordinates.tests.accuracy.test_ecliptic (top-level), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_celestial_transformations (top-level), astropy.coordinates.tests.test_erfa_astrom (top-level), astropy.coordinates.tests.test_frames (top-level), astropy.coordinates.tests.test_funcs (top-level), astropy.coordinates.tests.test_iau_fullstack (top-level), astropy.coordinates.tests.test_icrs_observed_transformations (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_matching (delayed), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_shape_manipulation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_sky_coord_velocities (top-level), astropy.coordinates.tests.test_skyoffset_transformations (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.coordinates.tests.test_velocity_corrs (top-level), astropy.io.ascii.tests.test_cds (top-level), astropy.io.misc.asdf.tags.coordinates.skycoord (top-level), astropy.io.misc.tests.test_pandas (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.nddata.tests.test_utils (top-level), astropy.table.tests.test_table (top-level), astropy.table.tests.test_jsviewer (top-level), astropy.table.tests.test_mixin (top-level), astropy.table.tests.test_operations (top-level), astropy.table.tests.test_pickle (top-level), astropy.time.tests.test_corrs (top-level), astropy.utils.masked.tests.test_containers (top-level), astropy.utils.tests.test_data_info (top-level), astropy.visualization.wcsaxes.tests.test_display_world_coordinates (top-level), astropy.visualization.wcsaxes.tests.test_images (top-level), astropy.visualization.wcsaxes.tests.test_misc (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.tests.test_utils (top-level), astropy.wcs.tests.test_wcs (top-level), astropy.wcs.wcsapi.conftest (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level), astropy.wcs.wcsapi.tests.test_high_level_api (top-level), astropy.wcs.wcsapi.tests.test_high_level_wcs_wrapper (top-level), astropy.wcs.wcsapi.wrappers.tests.test_sliced_wcs (top-level)
missing module named pydot - imported by networkx.drawing.nx_pydot (delayed)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), cffi.lock (conditional, optional), astropy.extern._strptime (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pickleshare (optional), pycparser.ply.yacc (delayed, optional), astropy.extern.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named PIL._imagingagg - imported by PIL (delayed, conditional, optional), PIL.ImageDraw (delayed, conditional, optional)
missing module named pygraphviz - imported by networkx.drawing.nx_agraph (delayed, optional)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), scipy.optimize._optimize (top-level), scipy.linalg._decomp (top-level), scipy.interpolate._pade (top-level), scipy.signal._lti_conversion (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.max - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.prod - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), scipy.sparse.linalg._isolve.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_schur (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._tnc (top-level), scipy.optimize._slsqp_py (top-level), scipy.stats._stats_py (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._fitpack_impl (top-level), scipy.interpolate._fitpack2 (top-level), scipy.integrate._ode (top-level), scipy._lib._finite_differences (top-level), scipy.stats._morestats (top-level), scipy.misc._common (top-level), scipy.signal._bsplines (top-level), scipy.signal._filter_design (top-level), scipy.signal._lti_conversion (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), scipy.optimize._minpack_py (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), IPython.core.magics.namespace (delayed, conditional, optional), astropy.cosmology.funcs.comparison (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._mstats_basic (top-level), scipy.stats._mstats_extras (top-level), pandas.compat.numpy.function (top-level)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), numpy.testing.overrides (top-level)
missing module named dummy_threading - imported by psutil._compat (optional), requests.cookies (optional)
missing module named numpy.isinf - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.isnan - imported by numpy (top-level), numpy.testing._private.utils (top-level), astropy.io.ascii.tests.common (optional)
missing module named numpy.isfinite - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._matfuncs (top-level), scipy.optimize._slsqp_py (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy.optimize._lbfgsb_py (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named tputil - imported by jinja2.debug (conditional, optional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named matplotlib.axes.Axes - imported by matplotlib.axes (delayed), matplotlib.legend (delayed), matplotlib.projections.geo (top-level), matplotlib.projections.polar (top-level), mpl_toolkits.mplot3d.axes3d (top-level), matplotlib.figure (top-level), matplotlib.pyplot (top-level), astropy.visualization.wcsaxes.core (top-level), mpl_toolkits.axes_grid1.axes_size (top-level), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.core (conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional), pandas.plotting._matplotlib.misc (conditional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), asttokens.asttokens (top-level), html5lib._inputstream (top-level), html5lib.filters.sanitizer (top-level), bleach._vendor.html5lib._inputstream (top-level), bleach._vendor.html5lib.filters.sanitizer (top-level)
missing module named 'six.moves.urllib' - imported by 'six.moves.urllib' (top-level)
missing module named six.moves.xrange - imported by six.moves (top-level), asttokens.asttokens (top-level)
missing module named StringIO - imported by six (conditional), urllib3.packages.six (conditional), requests.compat (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named argcomplete - imported by traitlets.config.loader (delayed, optional), traitlets.config.argcomplete_config (optional)
missing module named traitlets.config.Application - imported by traitlets.config (delayed, conditional), traitlets.log (delayed, conditional), ipykernel.kernelspec (top-level)
missing module named jsonschema.ErrorTree - imported by jsonschema (top-level), nbformat.json_compat (top-level)
missing module named rpds.List - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieSet - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieMap - imported by rpds (top-level), referencing._core (top-level), jsonschema._types (top-level), jsonschema.validators (top-level)
missing module named Cookie - imported by requests.compat (conditional)
missing module named cookielib - imported by requests.compat (conditional)
missing module named urllib2 - imported by requests.compat (conditional), ephem.cities (conditional)
missing module named urlparse - imported by requests.compat (conditional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named Queue - imported by urllib3.util.queue (conditional), debugpy.common.compat (conditional)
missing module named "'urllib3.packages.six.moves.urllib'.parse" - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.util.response (top-level), urllib3.connectionpool (top-level), 'urllib3.packages.six.moves.urllib' (top-level), urllib3.util.queue (top-level)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional)
missing module named 'cryptography.hazmat' - imported by urllib3.contrib.pyopenssl (top-level)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by pygments.lexer (delayed, conditional, optional), requests.compat (optional), requests (optional), requests.packages (optional), bs4.dammit (optional)
missing module named unicodedata2 - imported by charset_normalizer.utils (optional)
missing module named importlib_resources - imported by matplotlib.style.core (conditional), jsonschema_specifications._core (optional)
missing module named rfc3987 - imported by jsonschema._format (optional)
missing module named cached_property - imported by fqdn._compat (conditional)
missing module named numpydoc - imported by jedi.inference.docstrings (delayed)
missing module named trio - imported by IPython.core.async_helpers (delayed), ipykernel.trio_runner (top-level)
missing module named curio - imported by IPython.core.async_helpers (delayed)
missing module named docrepr - imported by IPython.core.interactiveshell (optional)
missing module named pexpect - imported by IPython.utils._process_posix (top-level), jupyter_client.ssh.tunnel (optional)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (top-level), IPython.core.oinspect (top-level)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named pygments.formatters.LatexFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed), IPython.core.oinspect (top-level), stack_data.core (delayed)
missing module named _typeshed - imported by prompt_toolkit.eventloop.inputhook (conditional)
missing module named prompt_toolkit.filters.is_done - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.base (top-level), prompt_toolkit.shortcuts.progress_bar.base (top-level), prompt_toolkit.shortcuts.prompt (top-level)
missing module named prompt_toolkit.filters.has_completions - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.toolbars (top-level), prompt_toolkit.widgets.dialogs (top-level), IPython.terminal.shortcuts (top-level)
missing module named prompt_toolkit.filters.vi_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), IPython.terminal.shortcuts (top-level)
missing module named prompt_toolkit.filters.emacs_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), prompt_toolkit.key_binding.bindings.emacs (top-level), IPython.terminal.shortcuts (top-level)
missing module named prompt_toolkit.filters.is_searching - imported by prompt_toolkit.filters (top-level), prompt_toolkit.search (top-level), prompt_toolkit.key_binding.bindings.search (top-level), prompt_toolkit.key_binding.bindings.vi (top-level)
missing module named prompt_toolkit.filters.vi_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.document (top-level), prompt_toolkit.key_binding.bindings.page_navigation (top-level), prompt_toolkit.widgets.toolbars (top-level), IPython.terminal.shortcuts (top-level)
missing module named 'prompt_toolkit.key_binding.key_bindings.vi' - imported by prompt_toolkit.key_binding.vi_state (conditional)
missing module named backports - imported by wcwidth.wcwidth (optional)
missing module named 'yapf.yapflib' - imported by IPython.terminal.interactiveshell (delayed)
missing module named yapf - imported by IPython.terminal.interactiveshell (delayed)
missing module named black - imported by IPython.terminal.interactiveshell (delayed)
missing module named prompt_toolkit.filters.vi_insert_multiple_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.processors (top-level)
missing module named pathlib2 - imported by pickleshare (optional)
missing module named cloudpickle - imported by ipykernel.pickleutil (delayed)
missing module named dill - imported by ipykernel.pickleutil (delayed)
missing module named 'ipyparallel.serialize' - imported by ipykernel.serialize (optional), ipykernel.pickleutil (top-level)
missing module named ipykernel.connect_qtconsole - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.get_connection_info - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.get_connection_file - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named 'gi.repository' - imported by ipykernel.gui.gtk3embed (top-level)
missing module named gi - imported by matplotlib.cbook (delayed, conditional), ipykernel.gui.gtk3embed (top-level)
missing module named gtk - imported by ipykernel.gui.gtkembed (top-level)
missing module named gobject - imported by ipykernel.gui.gtkembed (top-level)
missing module named wx - imported by IPython.lib.guisupport (delayed), ipykernel.eventloops (delayed)
missing module named PySide2 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named sip - imported by IPython.external.qt_loaders (delayed, optional), PyQt5 (top-level)
missing module named ipyparallel - imported by ipykernel.zmqshell (delayed, conditional)
missing module named _subprocess - imported by jupyter_client.launcher (delayed, conditional, optional), ipykernel.parentpoller (delayed, optional)
missing module named appnope - imported by ipykernel.ipkernel (delayed, conditional)
missing module named '_pydevd_bundle.pydevd_api' - imported by ipykernel.debugger (delayed)
missing module named '_pydevd_bundle.pydevd_suspended_frames' - imported by ipykernel.debugger (optional)
missing module named _pydevd_bundle - imported by debugpy._vendored.force_pydevd (top-level), ipykernel.debugger (optional)
missing module named pydevd_file_utils - imported by debugpy.server.api (top-level)
missing module named '_pydevd_bundle.pydevd_constants' - imported by debugpy.server.api (top-level)
missing module named pydevd - imported by debugpy._vendored.force_pydevd (top-level), debugpy.server.api (top-level)
missing module named netifaces - imported by jupyter_client.localinterfaces (delayed)
missing module named paramiko - imported by jupyter_client.ssh.tunnel (optional)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), jupyter_client.ssh.tunnel (top-level)
missing module named jnius - imported by platformdirs.android (delayed, optional)
missing module named gevent - imported by zmq.green.core (top-level), zmq.green.poll (top-level)
missing module named 'gevent.core' - imported by zmq.green.core (delayed, optional)
missing module named 'gevent.hub' - imported by zmq.green.core (top-level)
missing module named 'gevent.event' - imported by zmq.green.core (top-level)
missing module named zmq.backend.zmq_version_info - imported by zmq.backend (top-level), zmq.sugar.version (top-level)
missing module named zmq.backend.Frame - imported by zmq.backend (top-level), zmq.sugar.frame (top-level), zmq.sugar.tracker (top-level)
missing module named zmq.backend.Socket - imported by zmq.backend (top-level), zmq.sugar.socket (top-level)
missing module named zmq.backend.zmq_poll - imported by zmq.backend (top-level), zmq.sugar.poll (top-level)
missing module named pyczmq - imported by zmq.sugar.context (delayed)
missing module named zmq.backend.Context - imported by zmq.backend (top-level), zmq.sugar.context (top-level)
missing module named zmq.ZMQError - imported by zmq (delayed, optional), zmq.sugar.attrsettr (delayed, optional)
missing module named zmq.backend.zmq_errno - imported by zmq.backend (delayed), zmq.error (delayed, conditional)
missing module named zmq.backend.strerror - imported by zmq.backend (delayed), zmq.error (delayed)
missing module named zmq.zmq_version_info - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named zmq.zmq_version - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named zmq.libzmq - imported by zmq (delayed, optional)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_ldl (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level), scipy.stats._morestats (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._matfuncs (top-level), scipy.stats._morestats (top-level)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional)
missing module named pytest - imported by scipy._lib._testutils (delayed), pandas._testing._io (delayed), pandas._testing (delayed), pandas.util._tester (delayed, optional), networkx.classes.backends (conditional, optional), astropy.visualization.wcsaxes (optional), astropy.tests.runner (delayed, conditional), astropy.config.tests.test_configs (top-level), astropy.conftest (top-level), astropy.constants.tests.test_constant (top-level), astropy.constants.tests.test_pickle (top-level), astropy.tests.helper (top-level), astropy.constants.tests.test_prior_version (top-level), astropy.constants.tests.test_sciencestate (top-level), astropy.convolution.tests.test_convolve (top-level), astropy.convolution.tests.test_convolve_fft (top-level), astropy.convolution.tests.test_convolve_kernels (top-level), astropy.convolution.tests.test_convolve_models (top-level), astropy.convolution.tests.test_convolve_nddata (top-level), astropy.convolution.tests.test_discretize (top-level), astropy.modeling.tests.test_models (top-level), astropy.convolution.tests.test_kernel_class (top-level), astropy.convolution.tests.test_pickle (top-level), astropy.coordinates.tests.accuracy.test_altaz_icrs (top-level), astropy.coordinates.tests.accuracy.test_ecliptic (top-level), astropy.coordinates.tests.test_angle_generators (top-level), astropy.coordinates.tests.test_angles (top-level), astropy.coordinates.tests.test_angular_separation (top-level), astropy.coordinates.tests.test_api_ape5 (top-level), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_atc_replacements (top-level), astropy.coordinates.tests.test_celestial_transformations (top-level), astropy.coordinates.tests.test_distance (top-level), astropy.coordinates.tests.test_earth (top-level), astropy.coordinates.tests.test_earth_orientation (top-level), astropy.coordinates.tests.test_erfa_astrom (top-level), astropy.coordinates.tests.test_finite_difference_velocities (top-level), astropy.coordinates.tests.test_formatting (top-level), astropy.coordinates.tests.test_frames (top-level), astropy.coordinates.tests.test_representation (top-level), astropy.coordinates.tests.test_frames_with_velocity (top-level), astropy.coordinates.tests.test_funcs (top-level), astropy.coordinates.tests.test_geodetic_representations (top-level), astropy.coordinates.tests.test_iau_fullstack (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_matching (top-level), astropy.coordinates.tests.test_matrix_utilities (top-level), astropy.coordinates.tests.test_name_resolve (top-level), astropy.coordinates.tests.test_pickle (top-level), astropy.coordinates.tests.test_polarization (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_representation_arithmetic (top-level), astropy.coordinates.tests.test_representation_methods (top-level), astropy.coordinates.tests.test_shape_manipulation (top-level), astropy.coordinates.tests.test_sites (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_sky_coord_velocities (top-level), astropy.coordinates.tests.test_skyoffset_transformations (top-level), astropy.coordinates.tests.test_solar_system (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.coordinates.tests.test_spectral_quantity (top-level), astropy.coordinates.tests.test_transformations (top-level), astropy.coordinates.tests.test_utils (top-level), astropy.coordinates.tests.test_velocity_corrs (top-level), astropy.cosmology.tests.helper (top-level), astropy.cosmology.flrw.tests.test_base (top-level), astropy.cosmology.tests.test_core (top-level), astropy.cosmology.tests.test_connect (top-level), astropy.cosmology.io.tests.test_cosmology (top-level), astropy.cosmology.io.tests.base (top-level), astropy.cosmology.io.tests.test_ecsv (top-level), astropy.cosmology.io.tests.test_html (top-level), astropy.cosmology.io.tests.test_json (top-level), astropy.cosmology.io.tests.test_mapping (top-level), astropy.cosmology.io.tests.test_model (top-level), astropy.cosmology.io.tests.test_row (top-level), astropy.cosmology.io.tests.test_table (top-level), astropy.cosmology.io.tests.test_yaml (top-level), astropy.cosmology.tests.test_parameter (top-level), astropy.cosmology.flrw.tests.test_init (top-level), astropy.cosmology.flrw.tests.test_lambdacdm (top-level), astropy.cosmology.flrw.tests.test_w (top-level), astropy.cosmology.flrw.tests.test_w0cdm (top-level), astropy.cosmology.flrw.tests.test_w0wacdm (top-level), astropy.cosmology.flrw.tests.test_w0wzcdm (top-level), astropy.cosmology.flrw.tests.test_wpwazpcdm (top-level), astropy.cosmology.funcs.tests.test_comparison (top-level), astropy.cosmology.funcs.tests.test_funcs (top-level), astropy.cosmology.tests.test_parameters (top-level), astropy.cosmology.tests.test_realizations (top-level), astropy.cosmology.tests.test_units (top-level), astropy.cosmology.tests.test_utils (top-level), astropy.io.ascii.tests.test_c_reader (top-level), astropy.io.ascii.tests.test_cds (top-level), astropy.io.ascii.tests.test_cds_header_from_readme (top-level), astropy.io.ascii.tests.test_compressed (top-level), astropy.io.ascii.tests.test_connect (top-level), astropy.io.ascii.tests.test_ecsv (top-level), astropy.io.ascii.tests.test_fixedwidth (top-level), astropy.io.ascii.tests.test_html (top-level), astropy.io.ascii.tests.test_ipac_definitions (top-level), astropy.io.ascii.tests.test_qdp (top-level), astropy.io.ascii.tests.test_read (top-level), astropy.io.ascii.tests.test_write (top-level), astropy.io.fits._tiled_compression.tests.conftest (top-level), astropy.io.fits._tiled_compression.tests.test_fitsio (top-level), astropy.io.fits._tiled_compression.tests.test_tiled_compression (top-level), astropy.io.fits.tests.conftest (top-level), astropy.io.fits.tests.test_checksum (top-level), astropy.io.fits.tests.test_table (top-level), astropy.io.fits.tests.test_connect (top-level), astropy.io.fits.tests.test_core (top-level), astropy.io.fits.tests.test_compression_failures (top-level), astropy.io.fits.tests.test_convenience (top-level), astropy.io.fits.tests.test_diff (top-level), astropy.io.fits.tests.test_fitscheck (top-level), astropy.io.fits.tests.test_fitsdiff (top-level), astropy.io.fits.tests.test_fitsheader (top-level), astropy.io.fits.tests.test_fitsinfo (top-level), astropy.io.fits.tests.test_fitstime (top-level), astropy.io.fits.tests.test_fsspec (top-level), astropy.io.fits.tests.test_groups (top-level), astropy.io.fits.tests.test_hdulist (top-level), astropy.io.fits.tests.test_header (top-level), astropy.io.fits.tests.test_image (top-level), astropy.io.fits.tests.test_image_dask (top-level), astropy.io.fits.tests.test_tilde_path (top-level), astropy.io.fits.tests.test_uint (top-level), astropy.io.fits.tests.test_util (top-level), astropy.io.misc.tests.test_hdf5 (top-level), astropy.io.misc.tests.test_pandas (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_pickle_helpers (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.io.registry.tests.test_registries (top-level), astropy.io.votable.tests.converter_test (top-level), astropy.io.votable.tests.exception_test (top-level), astropy.io.votable.tests.table_test (top-level), astropy.io.votable.tests.tree_test (top-level), astropy.io.votable.tests.ucd_test (top-level), astropy.io.votable.tests.util_test (top-level), astropy.io.votable.tests.vo_test (top-level), astropy.modeling.tests.test_bounding_box (top-level), astropy.modeling.tests.test_compound (top-level), astropy.modeling.tests.test_constraints (top-level), astropy.modeling.tests.test_convolution (top-level), astropy.modeling.tests.test_core (top-level), astropy.modeling.tests.test_fitters (top-level), astropy.modeling.tests.test_functional_models (top-level), astropy.modeling.tests.test_input (top-level), astropy.modeling.tests.test_mappings (top-level), astropy.modeling.tests.test_math_func (top-level), astropy.modeling.tests.test_model_sets (top-level), astropy.modeling.tests.test_models_quantities (top-level), astropy.modeling.tests.test_parameters (top-level), astropy.modeling.tests.test_physical_models (top-level), astropy.modeling.tests.test_pickle (top-level), astropy.modeling.tests.test_polynomial (top-level), astropy.modeling.tests.test_projections (top-level), astropy.modeling.tests.test_quantities_evaluation (top-level), astropy.modeling.tests.test_quantities_fitting (top-level), astropy.modeling.tests.test_quantities_model (top-level), astropy.modeling.tests.test_quantities_parameters (top-level), astropy.modeling.tests.test_quantities_rotations (top-level), astropy.modeling.tests.test_rotations (top-level), astropy.modeling.tests.test_separable (top-level), astropy.modeling.tests.test_spline (top-level), astropy.modeling.tests.test_statistics (top-level), astropy.modeling.tests.test_units_mapping (top-level), astropy.modeling.tests.test_utils (top-level), astropy.nddata.mixins.tests.test_ndarithmetic (top-level), astropy.nddata.mixins.tests.test_ndslicing (top-level), astropy.nddata.tests.test_bitmask (top-level), astropy.nddata.tests.test_blocks (top-level), astropy.nddata.tests.test_ccddata (top-level), astropy.nddata.tests.test_compat (top-level), astropy.nddata.tests.test_decorators (top-level), astropy.nddata.tests.test_flag_collection (top-level), astropy.nddata.tests.test_nddata (top-level), astropy.nddata.tests.test_nduncertainty (top-level), astropy.utils.tests.test_metadata (top-level), astropy.nddata.tests.test_utils (top-level), astropy.samp.tests.test_client (top-level), astropy.samp.tests.test_hub (top-level), astropy.samp.tests.test_hub_script (top-level), astropy.samp.tests.test_standard_profile (top-level), astropy.samp.tests.test_web_profile (top-level), astropy.stats.tests.test_bayesian_blocks (top-level), astropy.stats.tests.test_biweight (top-level), astropy.stats.tests.test_circstats (top-level), astropy.stats.tests.test_funcs (top-level), astropy.stats.tests.test_histogram (top-level), astropy.stats.tests.test_jackknife (top-level), astropy.stats.tests.test_sigma_clipping (top-level), astropy.stats.tests.test_spatial (top-level), astropy.table.mixins.tests.test_dask (top-level), astropy.table.mixins.tests.test_registry (top-level), astropy.table.tests.conftest (top-level), astropy.table.tests.test_array (top-level), astropy.table.tests.test_bst (top-level), astropy.table.tests.test_column (top-level), astropy.table.tests.test_groups (top-level), astropy.table.tests.test_index (top-level), astropy.table.tests.test_table (top-level), astropy.table.tests.test_info (top-level), astropy.table.tests.test_init_table (top-level), astropy.table.tests.test_item_access (top-level), astropy.table.tests.test_jsviewer (top-level), astropy.table.tests.test_masked (top-level), astropy.table.tests.test_mixin (top-level), astropy.table.tests.test_operations (top-level), astropy.table.tests.test_pprint (top-level), astropy.table.tests.test_row (top-level), astropy.table.tests.test_showtable (top-level), astropy.tests.test_logger (top-level), astropy.tests.tests.test_quantity_helpers (top-level), astropy.tests.tests.test_run_tests (top-level), astropy.tests.tests.test_runner (top-level), astropy.time.tests.test_basic (top-level), astropy.time.tests.test_comparisons (top-level), astropy.time.tests.test_corrs (top-level), astropy.time.tests.test_custom_formats (top-level), astropy.time.tests.test_delta (top-level), astropy.time.tests.test_fast_parser (top-level), astropy.time.tests.test_functions (top-level), astropy.time.tests.test_guess (top-level), astropy.time.tests.test_mask (top-level), astropy.time.tests.test_methods (top-level), astropy.time.tests.test_precision (top-level), astropy.time.tests.test_quantity_interaction (top-level), astropy.time.tests.test_sidereal (top-level), astropy.time.tests.test_update_leap_seconds (top-level), astropy.time.tests.test_ut1 (top-level), astropy.timeseries.io.tests.test_kepler (top-level), astropy.timeseries.periodograms.bls.tests.test_bls (top-level), astropy.timeseries.periodograms.lombscargle.implementations.tests.test_mle (top-level), astropy.timeseries.periodograms.lombscargle.implementations.tests.test_utils (top-level), astropy.timeseries.periodograms.lombscargle.tests.test_lombscargle (top-level), astropy.timeseries.periodograms.lombscargle.tests.test_statistics (top-level), astropy.timeseries.periodograms.lombscargle.tests.test_utils (top-level), astropy.timeseries.periodograms.lombscargle_multiband.tests.test_lombscargle_multiband (top-level), astropy.timeseries.tests.test_binned (top-level), astropy.timeseries.tests.test_common (top-level), astropy.timeseries.tests.test_downsample (top-level), astropy.timeseries.tests.test_sampled (top-level), astropy.uncertainty.tests.test_containers (top-level), astropy.uncertainty.tests.test_distribution (top-level), astropy.units.tests.test_aliases (top-level), astropy.units.tests.test_deprecated (top-level), astropy.units.tests.test_equivalencies (top-level), astropy.units.tests.test_format (top-level), astropy.units.tests.test_logarithmic (top-level), astropy.units.tests.test_physical (top-level), astropy.units.tests.test_quantity (top-level), astropy.units.tests.test_quantity_annotations (top-level), astropy.units.tests.test_quantity_array_methods (top-level), astropy.units.tests.test_quantity_decorator (top-level), astropy.units.tests.test_quantity_helpers (top-level), astropy.units.tests.test_quantity_non_ufuncs (top-level), astropy.units.tests.test_quantity_ufuncs (top-level), astropy.units.tests.test_structured (top-level), astropy.units.tests.test_structured_erfa_ufuncs (top-level), astropy.units.tests.test_units (top-level), astropy.utils.iers.tests.test_iers (top-level), astropy.utils.iers.tests.test_leap_second (top-level), astropy.utils.masked.tests.test_containers (top-level), astropy.utils.masked.tests.test_function_helpers (top-level), astropy.utils.masked.tests.test_masked (top-level), astropy.utils.masked.tests.test_functions (top-level), astropy.utils.masked.tests.test_table (top-level), astropy.utils.tests.test_codegen (top-level), astropy.utils.tests.test_collections (top-level), astropy.utils.tests.test_console (top-level), astropy.utils.tests.test_data (top-level), astropy.utils.tests.test_data_info (top-level), astropy.utils.tests.test_decorators (top-level), astropy.utils.tests.test_diff (top-level), astropy.utils.tests.test_introspection (top-level), astropy.utils.tests.test_misc (top-level), astropy.utils.tests.test_parsing (top-level), astropy.utils.tests.test_shapes (top-level), astropy.utils.tests.test_xml (top-level), astropy.visualization.scripts.tests.test_fits2bitmap (top-level), astropy.visualization.tests.test_histogram (top-level), astropy.visualization.tests.test_interval (top-level), astropy.visualization.tests.test_lupton_rgb (top-level), astropy.visualization.tests.test_norm (top-level), astropy.visualization.tests.test_stretch (top-level), astropy.visualization.tests.test_time (top-level), astropy.visualization.tests.test_units (top-level), astropy.visualization.wcsaxes.tests.test_coordinate_helpers (top-level), astropy.visualization.wcsaxes.tests.test_images (top-level), astropy.tests.figures.helpers (top-level), astropy.visualization.wcsaxes.tests.test_formatter_locator (top-level), astropy.visualization.wcsaxes.tests.test_grid_paths (top-level), astropy.visualization.wcsaxes.tests.test_misc (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.tests.conftest (top-level), astropy.wcs.tests.test_celprm (top-level), astropy.wcs.tests.test_pickle (top-level), astropy.wcs.tests.test_prjprm (top-level), astropy.wcs.tests.test_profiling (top-level), astropy.wcs.tests.test_tab (top-level), astropy.wcs.tests.test_utils (top-level), astropy.wcs.tests.test_wcs (top-level), astropy.wcs.tests.test_wcsprm (top-level), astropy.wcs.wcsapi.conftest (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level), astropy.wcs.wcsapi.tests.test_high_level_wcs_wrapper (top-level), astropy.wcs.wcsapi.tests.test_low_level_api (top-level), astropy.wcs.wcsapi.tests.test_utils (top-level), astropy.wcs.wcsapi.wrappers.tests.test_sliced_wcs (top-level)
missing module named sparse - imported by scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named numpy.random.randn - imported by numpy.random (top-level), scipy (top-level)
missing module named numpy.random.rand - imported by numpy.random (top-level), scipy (top-level)
missing module named scipy.special.j1 - imported by scipy.special (delayed, conditional), astropy.modeling.functional_models (delayed, conditional)
missing module named scipy.special.wofz - imported by scipy.special (delayed, conditional), astropy.modeling.functional_models (delayed, conditional)
missing module named scipy.special.betaincinv - imported by scipy.special (delayed, conditional), astropy.stats.funcs (delayed, conditional)
missing module named scipy.special.erfinv - imported by scipy.special (delayed, conditional), astropy.stats.funcs (delayed, conditional), astropy.stats.jackknife (delayed)
missing module named scipy.special.sph_jn - imported by scipy.special (delayed, conditional, optional), sympy.functions.special.bessel (delayed, conditional, optional)
missing module named scipy.special.hyp2f1 - imported by scipy.special (conditional), astropy.cosmology.flrw.lambdacdm (conditional)
missing module named scipy.special.ellipkinc - imported by scipy.special (conditional), astropy.cosmology.flrw.lambdacdm (conditional)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level), astropy.modeling.functional_models (delayed, conditional)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.gammainc - imported by scipy.special (top-level), scipy.stats._qmc (top-level)
missing module named scipy.special.ndtri - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._binomtest (top-level), scipy.stats._relative_risk (top-level), scipy.stats._odds_ratio (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ndtr - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.entr - imported by scipy.special (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.xlogy - imported by scipy.special (top-level), scipy.interpolate._rbf (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.rel_entr - imported by scipy.special (top-level), scipy.spatial.distance (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named scipy.spatial.Voronoi - imported by scipy.spatial (top-level), scipy.stats._qmc (top-level)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named numpy.power - imported by numpy (top-level), scipy.stats._kde (top-level)
missing module named numpy.NINF - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.logical_and - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.signal._bsplines (top-level)
missing module named numpy.floor - imported by numpy (top-level), scipy.special._basic (top-level), scipy.special._orthogonal (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._bsplines (top-level)
missing module named numpy.hypot - imported by numpy (top-level), scipy.stats._morestats (top-level)
missing module named numpy.log - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._morestats (top-level), astropy.cosmology.flrw.lambdacdm (top-level), scipy.signal._waveforms (top-level)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named numpy.sinh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.cosh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tanh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.log1p - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.ceil - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named scipy.special.gammaln - imported by scipy.special (top-level), scipy.special._spfun_stats (top-level), scipy.integrate._quadrature (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._hypotests (top-level), scipy.stats._multivariate (top-level), scipy.optimize._dual_annealing (top-level), astropy.timeseries.periodograms.lombscargle._statistics (delayed)
missing module named numpy.arccos - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named numpy.inexact - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.special._basic (top-level), scipy.optimize._minpack_py (top-level)
missing module named numpy.sign - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.conjugate - imported by numpy (top-level), scipy.linalg._matfuncs (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.logical_not - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named scipy.linalg.bandwidth - imported by scipy.linalg (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.single - imported by numpy (top-level), scipy.linalg._decomp_schur (top-level)
missing module named numpy.conj - imported by numpy (top-level), scipy.linalg._decomp (top-level)
missing module named numpy.arcsin - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.linalg.onenormest - imported by scipy.sparse.linalg (top-level), scipy.linalg._matfuncs_inv_ssq (top-level)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named numpy.double - imported by numpy (top-level), scipy.optimize._nnls (top-level)
missing module named numpy.greater - imported by numpy (top-level), scipy.optimize._minpack_py (top-level)
missing module named numpy.arccosh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arcsinh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arctan - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tan - imported by numpy (top-level), scipy.signal._bsplines (top-level), scipy.signal._filter_design (top-level)
missing module named 'sage.interfaces' - imported by sympy.core.basic (delayed)
missing module named gmpy2 - imported by mpmath.libmp.backend (conditional, optional), sympy.polys.domains.groundtypes (conditional), sympy.ntheory.primetest (delayed, conditional), sympy.testing.runtests (delayed, conditional)
missing module named 'sage.libs' - imported by mpmath.libmp.backend (conditional, optional), mpmath.libmp.libelefun (conditional, optional), mpmath.libmp.libmpf (conditional, optional), mpmath.libmp.libmpc (conditional, optional), mpmath.libmp.libhyper (delayed, conditional), mpmath.ctx_mp (conditional)
missing module named sage - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy - imported by mpmath.libmp.backend (conditional, optional), sympy.testing.runtests (delayed, conditional)
missing module named pysat - imported by sympy.logic.algorithms.minisat22_wrapper (delayed)
missing module named pycosat - imported by sympy.logic.algorithms.pycosat_wrapper (delayed)
missing module named 'sage.all' - imported by sympy.core.function (delayed)
missing module named pyglet - imported by sympy.plotting.pygletplot.plot (optional), sympy.plotting.pygletplot.plot_axes (top-level), sympy.printing.preview (delayed, conditional, optional), sympy.testing.runtests (delayed, conditional)
missing module named all - imported by sympy.testing.runtests (delayed, optional)
missing module named 'IPython.Shell' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.frontend' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.iplib' - imported by sympy.interactive.printing (delayed, optional)
missing module named 'pyglet.gl' - imported by sympy.plotting.pygletplot.plot_axes (top-level), sympy.plotting.pygletplot.util (top-level), sympy.plotting.pygletplot.plot_window (top-level), sympy.plotting.pygletplot.plot_camera (top-level), sympy.plotting.pygletplot.plot_rotation (top-level), sympy.plotting.pygletplot.plot_curve (top-level), sympy.plotting.pygletplot.plot_mode_base (top-level), sympy.plotting.pygletplot.plot_surface (top-level)
missing module named 'pyglet.window' - imported by sympy.plotting.pygletplot.managed_window (top-level), sympy.plotting.pygletplot.plot_controller (top-level), sympy.printing.preview (delayed, optional)
missing module named 'pyglet.clock' - imported by sympy.plotting.pygletplot.managed_window (top-level)
missing module named 'pyglet.image' - imported by sympy.printing.preview (delayed, optional)
missing module named lxml - imported by pandas.io.xml (delayed), html5lib.treewalkers.etree_lxml (top-level), bs4.builder._lxml (top-level), sympy.utilities.mathml (delayed), bleach._vendor.html5lib.treewalkers.etree_lxml (top-level)
missing module named py - imported by mpmath.tests.runtests (delayed, conditional)
missing module named xlwt - imported by pandas.io.excel._xlwt (delayed, conditional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named 'openpyxl.cell' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.styles' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.workbook' - imported by pandas.io.excel._openpyxl (delayed)
missing module named openpyxl - imported by pandas.io.excel._openpyxl (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (delayed)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named pyarrow - imported by pandas.core.arrays.masked (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays._arrow_utils (top-level), pandas.core.arrays.interval (delayed), pandas.core.arrays.period (delayed), pandas.io.feather_format (delayed), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), astropy.io.misc.parquet (delayed, optional)
missing module named numba - imported by pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.executor (delayed, conditional), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.var_ (top-level)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named 'lxml.etree' - imported by pandas.io.xml (delayed), pandas.io.formats.xml (delayed), html5lib.treebuilders.etree_lxml (top-level), pandas.io.html (delayed), networkx.readwrite.graphml (delayed, optional), bleach._vendor.html5lib.treebuilders.etree_lxml (top-level)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.string_arrow (conditional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named hypothesis - imported by pandas.util._tester (delayed, optional), astropy.io.fits.tests.test_image (top-level), astropy.time.tests.test_precision (top-level), astropy.utils.tests.test_shapes (top-level)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named iconv_codec - imported by bs4.dammit (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named html5lib.treebuilders._base - imported by html5lib.treebuilders (optional), bs4.builder._html5lib (optional)
missing module named 'genshi.core' - imported by html5lib.treewalkers.genshi (top-level), bleach._vendor.html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by html5lib.treewalkers.genshi (top-level)
missing module named 'chardet.universaldetector' - imported by html5lib._inputstream (delayed, conditional, optional), bleach._vendor.html5lib._inputstream (delayed, conditional, optional)
missing module named pandas.ExtensionArray - imported by pandas (conditional), pandas.core.construction (conditional)
missing module named pandas.UInt64Index - imported by pandas (conditional), pandas.core.dtypes.generic (conditional)
missing module named pandas.Int64Index - imported by pandas (conditional), pandas.core.dtypes.generic (conditional)
missing module named pandas.Float64Index - imported by pandas (conditional), pandas.core.dtypes.generic (conditional), pandas.core.internals.blocks (conditional), pandas.core.indexes.datetimes (conditional), pandas.core.internals.array_manager (conditional)
missing module named astropy._dev - imported by astropy.version (optional)
missing module named Carbon - imported by astropy.utils.console (delayed)
missing module named ipywidgets - imported by astropy.utils.console (delayed, conditional)
missing module named 'IPython.html' - imported by astropy.utils.console (delayed, conditional)
missing module named pyreadline - imported by astropy.utils.console (delayed, conditional, optional)
missing module named 'IPython.kernel' - imported by astropy.utils.console (delayed, conditional, optional)
missing module named 'IPython.zmq' - imported by astropy.utils.console (delayed, conditional, optional)
missing module named distributed - imported by fsspec.transaction (delayed)
missing module named zstandard - imported by fsspec.compression (optional)
missing module named lz4 - imported by fsspec.compression (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named tqdm - imported by fsspec.callbacks (delayed, optional)
missing module named compiler - imported by astropy.extern.configobj.configobj (delayed, conditional)
missing module named astropy.utils.IncompatibleShapeError - imported by astropy.utils (top-level), astropy.modeling.core (top-level)
missing module named astropy.utils.unbroadcast - imported by astropy.utils (top-level), astropy.coordinates.polarization (top-level), astropy.wcs.utils (top-level), astropy.coordinates.tests.test_polarization (top-level), astropy.wcs.tests.test_utils (top-level)
missing module named astropy.time.TimeBase - imported by astropy.time (delayed), astropy.table.table (delayed)
missing module named dask - imported by astropy.table.mixins.dask (top-level)
missing module named astropy.utils.masked.Masked - imported by astropy.utils.masked (delayed), astropy.utils.masked.function_helpers (delayed, conditional), astropy.nddata.nddata (top-level), astropy.table.table (top-level), astropy.table.operations (top-level), astropy.nddata.mixins.ndarithmetic (top-level), astropy.nddata.tests.test_nddata (top-level), astropy.table.tests.test_masked (top-level), astropy.units.tests.test_structured (top-level), astropy.utils.masked.tests.test_containers (top-level), astropy.utils.masked.tests.test_function_helpers (top-level), astropy.utils.masked.tests.test_masked (top-level), astropy.utils.masked.tests.test_table (top-level)
missing module named astropy.cosmology.flrw.this_is_not_a_variable - imported by astropy.cosmology.flrw (delayed), astropy.cosmology.flrw.tests.test_init (delayed)
missing module named astropy.units.add_enabled_units - imported by astropy.units (top-level), astropy.cosmology.connect (top-level)
missing module named astropy.cosmology.z_at_value - imported by astropy.cosmology (delayed), astropy.cosmology.units (delayed), astropy.coordinates.distances (delayed)
missing module named astropy.cosmology.Planck13 - imported by astropy.cosmology (top-level), astropy.cosmology.tests.test_units (top-level)
missing module named astropy.cosmology.Planck18 - imported by astropy.cosmology (top-level), astropy.cosmology.flrw.tests.test_base (top-level), astropy.cosmology.io.tests.test_yaml (top-level), astropy.cosmology.flrw.tests.test_w0wacdm (top-level), astropy.cosmology.funcs.tests.test_comparison (top-level)
missing module named astropy.cosmology.WMAP5 - imported by astropy.cosmology (delayed), astropy.coordinates.tests.test_distance (delayed)
missing module named astropy.units.MagUnit - imported by astropy.units (top-level), astropy.modeling.parameters (top-level)
missing module named astropy.units.UnitsError - imported by astropy.units (top-level), astropy.units.function.core (top-level), astropy.units.function.logarithmic (top-level), astropy.timeseries.sampled (top-level), astropy.visualization.wcsaxes.formatter_locator (top-level), astropy.modeling.functional_models (top-level), astropy.modeling.powerlaws (top-level), astropy.nddata.compat (top-level), astropy.modeling.core (top-level), astropy.modeling.tests.test_quantities_evaluation (top-level), astropy.modeling.tests.test_quantities_fitting (top-level), astropy.modeling.tests.test_quantities_parameters (top-level), astropy.nddata.mixins.tests.test_ndarithmetic (top-level), astropy.visualization.wcsaxes.tests.test_formatter_locator (top-level)
missing module named astropy.units.UnitConversionError - imported by astropy.units (top-level), astropy.time.core (top-level), astropy.units.function.core (top-level), astropy.units.function.logarithmic (top-level), astropy.nddata.nduncertainty (top-level), astropy.nddata.compat (top-level)
missing module named astropy.units.Unit - imported by astropy.units (top-level), astropy.table.column (top-level), astropy.nddata.nddata (top-level), astropy.coordinates.sky_coordinate_parsers (top-level), astropy.io.ascii.cds (top-level), astropy.coordinates.spectral_quantity (top-level), astropy.units.function.core (top-level), astropy.units.function.logarithmic (top-level), astropy.io.fits.convenience (delayed, conditional), astropy.nddata.nduncertainty (top-level), astropy.nddata.compat (top-level), astropy.io.ascii.tests.test_read (top-level), astropy.io.fits.tests.test_table (top-level), astropy.io.misc.asdf.tags.unit.unit (top-level), astropy.io.votable.tests.table_test (top-level), astropy.units.tests.test_format (top-level), astropy.units.tests.test_structured (top-level)
missing module named astropy.wcs.wcsapi.BaseLowLevelWCS - imported by astropy.wcs.wcsapi (top-level), astropy.visualization.wcsaxes.core (top-level), astropy.wcs.wcsapi.wrappers.base (top-level), astropy.nddata.nddata (top-level), astropy.nddata.mixins.ndslicing (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.wcsapi.conftest (top-level)
missing module named astropy.wcs.wcsapi.SlicedLowLevelWCS - imported by astropy.wcs.wcsapi (top-level), astropy.visualization.wcsaxes.wcsapi (top-level), astropy.nddata.nddata (top-level), astropy.nddata.mixins.ndslicing (top-level), astropy.nddata.tests.test_nddata (top-level), astropy.visualization.wcsaxes.tests.test_misc (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level)
missing module named 'dask.array' - imported by astropy.io.fits.util (delayed, optional)
missing module named astropy.io.fits.Card - imported by astropy.io.fits (top-level), astropy.io.fits.fitstime (top-level)
missing module named astropy.io.fits.append - imported by astropy.io.fits (top-level), astropy.io.fits.connect (top-level)
missing module named astropy.io.fits.TableHDU - imported by astropy.io.fits (top-level), astropy.io.fits.connect (top-level)
missing module named astropy.io.fits.HDUList - imported by astropy.io.fits (top-level), astropy.io.fits.connect (top-level), astropy.io.fits.tests.test_connect (top-level), astropy.io.fits.tests.test_fitsdiff (top-level), astropy.timeseries.io.tests.test_kepler (top-level)
missing module named astropy.io.fits.GroupsHDU - imported by astropy.io.fits (top-level), astropy.io.fits.connect (top-level)
missing module named astropy.io.fits.BinTableHDU - imported by astropy.io.fits (top-level), astropy.io.fits.connect (top-level), astropy.io.fits.tests.test_connect (top-level), astropy.timeseries.io.tests.test_kepler (top-level)
missing module named bottleneck - imported by astropy.stats.sigma_clipping (conditional)
missing module named scipy.misc.factorial - imported by scipy.misc (delayed, optional), astropy.stats.funcs (delayed, optional)
missing module named scipy.misc.comb - imported by scipy.misc (delayed, optional), astropy.stats.funcs (delayed, optional)
missing module named astropy.wcs.WCS - imported by astropy.wcs (top-level), astropy.visualization.wcsaxes.core (top-level), astropy.visualization.wcsaxes.wcsapi (top-level), astropy.nddata.ccddata (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.nddata._testing (top-level), astropy.nddata.mixins.tests.test_ndarithmetic (top-level), astropy.nddata.tests.test_ccddata (top-level), astropy.nddata.tests.test_compat (top-level), astropy.nddata.tests.test_decorators (top-level), astropy.nddata.tests.test_nddata (top-level), astropy.nddata.tests.test_utils (top-level), astropy.visualization.wcsaxes.tests.test_coordinate_helpers (top-level), astropy.visualization.wcsaxes.tests.test_display_world_coordinates (top-level), astropy.visualization.wcsaxes.tests.test_images (top-level), astropy.visualization.wcsaxes.tests.test_frame (top-level), astropy.visualization.wcsaxes.tests.test_misc (top-level), astropy.visualization.wcsaxes.tests.test_transform_coord_meta (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.tests.test_auxprm (top-level), astropy.wcs.wcsapi.conftest (top-level), astropy.wcs.wcsapi.tests.test_high_level_api (top-level), astropy.wcs.wcsapi.tests.test_utils (top-level)
missing module named astropy.constants.c - imported by astropy.constants (top-level), astropy.coordinates.sky_coordinate (top-level), astropy.coordinates.funcs (top-level), astropy.coordinates.solar_system (top-level), astropy.coordinates.spectral_coordinate (top-level), astropy.wcs.wcsapi.fitswcs (top-level), astropy.constants.tests.test_constant (delayed), astropy.constants.tests.test_prior_version (delayed), astropy.coordinates.tests.test_solar_system (top-level), astropy.coordinates.tests.test_spectral_coordinate (top-level), astropy.coordinates.tests.test_velocity_corrs (top-level)
missing module named numcodecs - imported by astropy.io.fits._tiled_compression.codecs (optional)
missing module named 'dask.utils' - imported by astropy.io.fits.hdu.image (delayed, conditional)
missing module named astropy.units.mag - imported by astropy.units (top-level), astropy.modeling.powerlaws (top-level)
missing module named astropy.units.Magnitude - imported by astropy.units (top-level), astropy.modeling.powerlaws (top-level), astropy.units.tests.test_photometric (top-level)
missing module named h5py - imported by astropy.io.misc.hdf5 (delayed, optional), astropy.io.misc.tests.test_hdf5 (delayed, conditional)
missing module named astropy.units.PrefixUnit - imported by astropy.units (delayed), astropy.units.utils (delayed), astropy.units.tests.test_format (top-level)
missing module named astropy.constants.G - imported by astropy.constants (delayed), astropy.units.tests.test_units (delayed)
missing module named astropy.constants.hbar - imported by astropy.constants (top-level), astropy.units.tests.test_physical (top-level)
missing module named astropy.constants.R_sun - imported by astropy.constants (top-level), astropy.coordinates.tests.accuracy.test_ecliptic (top-level)
missing module named astropy.constants.R_earth - imported by astropy.constants (top-level), astropy.coordinates.tests.accuracy.test_ecliptic (top-level)
missing module named astropy.constants.b_wien - imported by astropy.constants (delayed), astropy.constants.tests.test_constant (delayed)
missing module named astropy.constants.g0 - imported by astropy.constants (delayed), astropy.constants.tests.test_constant (delayed)
missing module named astropy.constants.e - imported by astropy.constants (delayed), astropy.constants.tests.test_constant (delayed), astropy.units.tests.test_units (delayed)
missing module named astropy.constants.h - imported by astropy.constants (delayed), astropy.constants.tests.test_constant (delayed), astropy.constants.tests.test_prior_version (delayed)
missing module named astropy.units.StructuredUnit - imported by astropy.units (top-level), astropy.table.column (top-level), astropy.units.quantity_helper.function_helpers (delayed), astropy.units.tests.test_structured (top-level)
missing module named astropy.units.dimensionless_unscaled - imported by astropy.units (delayed, conditional), astropy.units.quantity_helper.function_helpers (delayed, conditional), astropy.units.equivalencies (top-level), astropy.units.function.core (top-level), astropy.units.function.logarithmic (top-level), astropy.modeling.powerlaws (top-level), astropy.nddata.mixins.ndarithmetic (top-level), astropy.modeling.core (top-level)
missing module named astropy.units.dex - imported by astropy.units (delayed), astropy.units.quantity_helper.function_helpers (delayed), astropy.units.format.cds (delayed), astropy.units.tests.test_format (top-level)
missing module named astropy.units.LogQuantity - imported by astropy.units (delayed), astropy.units.quantity_helper.function_helpers (delayed)
missing module named astropy.units.percent - imported by astropy.units (delayed), astropy.units.quantity_helper.function_helpers (delayed)
missing module named astropy.units.QuantityInfo - imported by astropy.units (top-level), astropy.table.table (top-level), astropy.io.ascii.tests.test_ecsv (top-level)
missing module named astropy.units.Quantity - imported by astropy.units (top-level), astropy.table.column (top-level), astropy.cosmology.funcs.optimize (top-level), astropy.cosmology.utils (top-level), astropy.modeling.fitting (top-level), astropy.nddata.nddata (top-level), astropy.units.quantity_helper.function_helpers (delayed, conditional, optional), astropy.table.table (top-level), astropy.table.operations (top-level), astropy.units.function.core (top-level), astropy.io.fits.convenience (delayed, conditional), astropy.stats.circstats (top-level), astropy.stats.sigma_clipping (top-level), astropy.timeseries.sampled (top-level), astropy.timeseries.binned (top-level), astropy.modeling.functional_models (top-level), astropy.modeling.parameters (top-level), astropy.modeling.mappings (top-level), astropy.modeling.powerlaws (top-level), astropy.nddata.nduncertainty (top-level), astropy.modeling.core (top-level), astropy.modeling.bounding_box (top-level), astropy.io.votable.connect (top-level), astropy.constants.tests.test_constant (top-level), astropy.constants.tests.test_prior_version (top-level), astropy.io.misc.asdf.tags.unit.quantity (top-level), astropy.io.misc.asdf.tags.coordinates.frames (top-level), astropy.io.misc.asdf.tags.time.time (top-level), astropy.nddata.mixins.tests.test_ndarithmetic (top-level), astropy.table.tests.test_pickle (top-level), astropy.timeseries.tests.test_sampled (top-level), astropy.units.tests.test_quantity_annotations (top-level), astropy.units.tests.test_structured (top-level), astropy.utils.masked.tests.test_masked (top-level), astropy.utils.masked.tests.test_functions (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.tests.test_utils (top-level), astropy.wcs.wcsapi.conftest (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level), astropy.wcs.wcsapi.tests.test_high_level_api (top-level), astropy.wcs.wcsapi.wrappers.tests.test_sliced_wcs (top-level)
missing module named astropy.time.TimeDelta - imported by astropy.time (top-level), astropy.io.misc.yaml (top-level), astropy.table.table (delayed, conditional), astropy.utils.iers.iers (top-level), astropy.io.fits.fitstime (top-level), astropy.timeseries.io.kepler (top-level), astropy.timeseries.sampled (top-level), astropy.timeseries.binned (top-level), astropy.timeseries.downsample (top-level), astropy.timeseries.periodograms.bls.core (top-level), astropy.timeseries.periodograms.lombscargle.core (top-level), astropy.timeseries.periodograms.lombscargle_multiband.core (top-level), astropy.wcs.wcsapi.fitswcs (delayed), astropy.io.fits.tests.test_fitstime (top-level), astropy.io.misc.asdf.tags.time.timedelta (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.table.tests.test_table (top-level), astropy.table.tests.test_operations (top-level), astropy.time.tests.test_basic (top-level), astropy.time.tests.test_comparisons (top-level), astropy.time.tests.test_corrs (top-level), astropy.time.tests.test_delta (top-level), astropy.time.tests.test_functions (top-level), astropy.time.tests.test_precision (top-level), astropy.time.tests.test_quantity_interaction (top-level), astropy.timeseries.periodograms.bls.tests.test_bls (top-level), astropy.timeseries.periodograms.lombscargle.tests.test_lombscargle (top-level), astropy.timeseries.periodograms.lombscargle_multiband.tests.test_lombscargle_multiband (top-level), astropy.timeseries.tests.test_binned (top-level), astropy.timeseries.tests.test_sampled (top-level), astropy.utils.iers.tests.test_iers (top-level), astropy.utils.iers.tests.test_leap_second (top-level)
missing module named astropy.time.Time - imported by astropy.time (delayed), astropy.coordinates.attributes (delayed), astropy.coordinates.sky_coordinate (top-level), astropy.io.misc.yaml (top-level), astropy.table.table (delayed, conditional), astropy.coordinates.builtin_frames.utils (top-level), astropy.utils.iers.iers (top-level), astropy.coordinates.earth_orientation (top-level), astropy.coordinates.erfa_astrom (top-level), astropy.coordinates.builtin_frames.lsr (top-level), astropy.io.fits.connect (top-level), astropy.io.fits.fitstime (top-level), astropy.io.fits.convenience (delayed, conditional), astropy.timeseries.io.kepler (top-level), astropy.timeseries.sampled (top-level), astropy.timeseries.binned (top-level), astropy.timeseries.downsample (top-level), astropy.timeseries.periodograms.bls.core (top-level), astropy.timeseries.periodograms.lombscargle.core (top-level), astropy.timeseries.periodograms.lombscargle_multiband.core (top-level), astropy.visualization.time (top-level), astropy.wcs.wcsapi.fitswcs (delayed), astropy.wcs.utils (delayed), astropy.time.time_helper.function_helpers (delayed), astropy.table.index (delayed), astropy.coordinates.tests.accuracy.generate_spectralcoord_ref (conditional), astropy.coordinates.tests.accuracy.test_altaz_icrs (top-level), astropy.coordinates.tests.accuracy.test_ecliptic (top-level), astropy.coordinates.tests.accuracy.test_fk4_no_e_fk4 (top-level), astropy.coordinates.tests.accuracy.test_fk4_no_e_fk5 (top-level), astropy.coordinates.tests.accuracy.test_galactic_fk4 (top-level), astropy.coordinates.tests.accuracy.test_icrs_fk5 (top-level), astropy.coordinates.tests.test_arrays (top-level), astropy.coordinates.tests.test_atc_replacements (top-level), astropy.coordinates.tests.test_celestial_transformations (top-level), astropy.coordinates.tests.test_earth (top-level), astropy.coordinates.tests.test_earth_orientation (top-level), astropy.coordinates.tests.test_erfa_astrom (top-level), astropy.coordinates.tests.test_finite_difference_velocities (top-level), astropy.coordinates.tests.test_frames (top-level), astropy.coordinates.tests.test_funcs (top-level), astropy.coordinates.tests.test_iau_fullstack (top-level), astropy.coordinates.tests.test_icrs_observed_transformations (top-level), astropy.coordinates.tests.test_intermediate_transformations (top-level), astropy.coordinates.tests.test_regression (top-level), astropy.coordinates.tests.test_shape_manipulation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.coordinates.tests.test_skyoffset_transformations (top-level), astropy.coordinates.tests.test_solar_system (top-level), astropy.coordinates.tests.test_transformations (top-level), astropy.coordinates.tests.test_utils (top-level), astropy.coordinates.tests.test_velocity_corrs (top-level), astropy.io.ascii.tests.test_cds (top-level), astropy.io.fits.tests.test_connect (top-level), astropy.io.fits.tests.test_fitstime (top-level), astropy.io.misc.tests.test_parquet (top-level), astropy.io.misc.tests.test_yaml (top-level), astropy.table.tests.test_index (top-level), astropy.table.tests.test_table (top-level), astropy.table.tests.test_jsviewer (top-level), astropy.table.tests.test_masked (top-level), astropy.table.tests.test_operations (top-level), astropy.table.tests.test_pickle (top-level), astropy.time.tests.test_basic (top-level), astropy.time.tests.test_comparisons (top-level), astropy.time.tests.test_corrs (top-level), astropy.time.tests.test_custom_formats (top-level), astropy.time.tests.test_delta (top-level), astropy.time.tests.test_fast_parser (top-level), astropy.time.tests.test_functions (top-level), astropy.time.tests.test_guess (top-level), astropy.time.tests.test_mask (top-level), astropy.time.tests.test_methods (top-level), astropy.time.tests.test_pickle (top-level), astropy.time.tests.test_precision (top-level), astropy.time.tests.test_quantity_interaction (top-level), astropy.time.tests.test_sidereal (top-level), astropy.time.tests.test_update_leap_seconds (top-level), astropy.time.tests.test_ut1 (top-level), astropy.timeseries.periodograms.bls.tests.test_bls (top-level), astropy.timeseries.periodograms.lombscargle.tests.test_lombscargle (top-level), astropy.timeseries.periodograms.lombscargle_multiband.tests.test_lombscargle_multiband (top-level), astropy.timeseries.tests.test_binned (top-level), astropy.timeseries.tests.test_common (top-level), astropy.timeseries.tests.test_downsample (top-level), astropy.timeseries.tests.test_sampled (top-level), astropy.utils.iers.tests.test_iers (top-level), astropy.utils.iers.tests.test_leap_second (top-level), astropy.utils.masked.tests.test_containers (top-level), astropy.utils.tests.test_data_info (top-level), astropy.visualization.tests.test_time (top-level), astropy.visualization.wcsaxes.tests.test_display_world_coordinates (top-level), astropy.visualization.wcsaxes.tests.test_wcsapi (top-level), astropy.wcs.tests.test_utils (top-level), astropy.wcs.wcsapi.tests.test_fitswcs (top-level), astropy.wcs.wcsapi.wrappers.tests.test_sliced_wcs (top-level)
missing module named asdf - imported by astropy.io.misc.asdf.connect (delayed, optional), astropy.io.misc.asdf.conftest (optional), astropy.io.misc.asdf.tags.coordinates.frames (top-level), astropy.io.misc.asdf.tags.transform.compound (top-level), astropy.io.misc.asdf.tags.tests.helpers (delayed)
missing module named asdf_astropy - imported by astropy.table (conditional)
missing module named sortedcontainers - imported by astropy.table.soco (conditional)
missing module named astropy.utils.indent - imported by astropy.utils (top-level), astropy.coordinates.solar_system (top-level), astropy.io.fits.verify (top-level), astropy.io.fits.column (top-level), astropy.io.fits.hdu.hdulist (top-level), astropy.modeling.polynomial (top-level), astropy.modeling.core (top-level)
missing module named astropy.utils.check_broadcast - imported by astropy.utils (top-level), astropy.coordinates.baseframe (top-level), astropy.modeling.polynomial (top-level), astropy.modeling.core (top-level)
missing module named astropy.utils.format_exception - imported by astropy.utils (top-level), astropy.coordinates.angle_formats (top-level)
missing module named astropy.utils.isiterable - imported by astropy.utils (top-level), astropy.cosmology.utils (top-level), astropy.modeling.spline (top-level), astropy.units.quantity_helper.function_helpers (top-level), astropy.coordinates.angles (top-level), astropy.table.table (top-level), astropy.coordinates.funcs (top-level), astropy.io.fits.column (top-level), astropy.io.fits.header (top-level), astropy.io.fits.hdu.image (top-level), astropy.stats.sigma_clipping (top-level), astropy.wcs.wcsapi.wrappers.sliced_wcs (top-level), astropy.modeling.parameters (top-level), astropy.modeling.core (top-level), astropy.modeling.bounding_box (top-level), astropy.config.configuration (delayed), astropy.coordinates.tests.test_representation (top-level), astropy.coordinates.tests.test_sky_coord (top-level), astropy.time.tests.test_basic (top-level), astropy.units.tests.test_quantity (top-level)
missing module named astropy.utils.ShapedLikeNDArray - imported by astropy.utils (top-level), astropy.time.core (top-level), astropy.coordinates.representation (top-level), astropy.coordinates.attributes (top-level), astropy.coordinates.baseframe (top-level), astropy.coordinates.sky_coordinate (top-level), astropy.table.table (top-level)
missing module named erfa._dev - imported by erfa.version (optional)
missing module named astropy.units.nmgy - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.mgy - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.erg - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.STflux - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.Jy - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.ABflux - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.AA - imported by astropy.units (top-level), astropy.units.tests.test_photometric (top-level)
missing module named astropy.units.deg - imported by astropy.units (top-level), astropy.table.tests.test_pickle (top-level)
missing module named astropy.units.CompositeUnit - imported by astropy.units (top-level), astropy.units.function.logarithmic (top-level)
missing module named astropy.units.UnitTypeError - imported by astropy.units (top-level), astropy.units.function.core (top-level), astropy.units.function.logarithmic (top-level)
missing module named astropy.units.UnitBase - imported by astropy.units (top-level), astropy.units.function.core (top-level), astropy.io.misc.asdf.tags.unit.unit (top-level), astropy.units.tests.test_format (top-level), astropy.units.tests.test_structured (top-level)
missing module named astropy.units.IrreducibleUnit - imported by astropy.units (top-level), astropy.coordinates.sky_coordinate_parsers (top-level)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named jplephem - imported by astropy.coordinates.solar_system (delayed, optional)
missing module named extension_helpers - imported by astropy.utils.xml.setup_package (top-level), astropy.wcs.setup_package (top-level)
missing module named 'hypothesis.extra' - imported by astropy.io.fits.tests.test_image (top-level), astropy.time.tests.test_precision (top-level), astropy.utils.tests.test_shapes (top-level)
missing module named test_package - imported by astropy.utils.tests.test_data (delayed, optional)
missing module named 'py.path' - imported by astropy.utils.tests.test_data (top-level)
missing module named 'hypothesis.strategies' - imported by astropy.time.tests.test_precision (top-level)
missing module named coverage - imported by astropy.tests.command (delayed, optional)
missing module named 'asdf.types' - imported by astropy.io.misc.asdf.types (top-level), astropy.io.misc.asdf.tags.tests.helpers (delayed)
missing module named 'asdf.exceptions' - imported by astropy.io.misc.asdf.extension (top-level), astropy.io.misc.asdf.types (top-level), astropy.io.misc.asdf.tags.transform.compound (top-level), astropy.io.misc.asdf.tags.tests.helpers (delayed)
missing module named 'asdf.tags' - imported by astropy.io.misc.asdf.tags.unit.quantity (top-level), astropy.io.misc.asdf.tags.coordinates.spectralcoord (top-level), astropy.io.misc.asdf.tags.table.table (top-level)
missing module named 'asdf.versioning' - imported by astropy.io.misc.asdf.tags.time.time (top-level), astropy.io.misc.asdf.tags.transform.basic (top-level), astropy.io.misc.asdf.tags.transform.polynomial (top-level)
missing module named 'asdf.tests' - imported by astropy.io.misc.asdf.tags.transform.compound (delayed), astropy.io.misc.asdf.tags.tests.helpers (delayed)
missing module named 'asdf.schema' - imported by astropy.io.misc.asdf.tags.tests.helpers (delayed)
missing module named 'asdf.util' - imported by astropy.io.misc.asdf.extension (top-level)
missing module named 'asdf.extension' - imported by astropy.io.misc.asdf.extension (top-level)
missing module named objgraph - imported by astropy.io.fits.tests.test_table (optional)
missing module named fitsio - imported by astropy.io.fits._tiled_compression.tests.test_fitsio (conditional)
missing module named ply - imported by astropy.extern.ply.cpp (conditional)
missing module named skyfield - imported by astropy.coordinates.tests.test_solar_system (conditional)
missing module named pytest_remotedata - imported by astropy.coordinates.tests.test_name_resolve (top-level)
missing module named 'starlink.Ast' - imported by astropy.coordinates.tests.accuracy.generate_ref_ast (delayed)
missing module named starlink - imported by astropy.coordinates.tests.accuracy.generate_ref_ast (delayed)
missing module named pytest_astropy_header - imported by astropy.conftest (optional)
missing module named xdist - imported by astropy.tests.runner (delayed, conditional, optional)
missing module named pytest_pep8 - imported by astropy.tests.runner (delayed, conditional, optional)
