2025-03-23 12:57:38,030 - WARNING - Start 开始
2025-03-23 12:59:51,795 - WARNING - Start 开始
2025-03-23 13:01:07,088 - WARNING - Start 开始
2025-03-23 13:13:39,758 - WARNING - Start 开始
2025-03-23 13:14:18,345 - WARNING - Start 开始
2025-03-23 13:17:25,218 - WARNING - Start 开始
2025-03-23 13:44:26,989 - WARNING - Start 开始
2025-03-23 13:44:51,322 - WARNING - Start 开始
2025-03-23 13:45:24,098 - WARNING - Start 开始
2025-03-23 13:45:54,533 - ERROR - Error processing message
Traceback (most recent call last):
  File "C:/gitroot/python_tests/test_gcn/test_gcn_kafka_example.py", line 150, in <module>
    json_format_test['taskName'] = f'GRB_{time_str}_test_{json_format_test["id"][0]}'
KeyError: 'id'
2025-03-23 13:45:57,103 - ERROR - Error processing message
Traceback (most recent call last):
  File "C:/gitroot/python_tests/test_gcn/test_gcn_kafka_example.py", line 150, in <module>
    json_format_test['taskName'] = f'GRB_{time_str}_test_{json_format_test["id"][0]}'
KeyError: 'id'
2025-03-23 13:48:11,910 - WARNING - Start 开始
