import os
import portalocker

class ProcessLock:
    _instance = None

    def __new__(cls, lockfile=".singleton.lock"):
        if not cls._instance:
            cls._instance = super().__new__(cls)
            cls._instance.lockfile = os.path.abspath(lockfile)
            cls._instance.file = None
        return cls._instance

    def __enter__(self):
        self.file = open(self.lockfile, "w")
        portalocker.lock(self.file, portalocker.LOCK_EX | portalocker.LOCK_NB)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        portalocker.unlock(self.file)
        self.file.close()
        os.remove(self.lockfile)

# 使用示例
try:
    with ProcessLock():
        print("Acquired lock, singleton process running...")


        # 你的业务代码
except portalocker.LockException:
    print("Another instance is already running")
while True:
    pass
